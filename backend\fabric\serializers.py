from rest_framework import serializers
from .models import Supplier, FabricDefinition, FabricVariant


COLOR_MAP = {
    "#000000": "Black",
    "#FFFFFF": "White",
    "#6f2f2f": "Dark Red",
    # add other mappings as needed
}
class SupplierSerializer(serializers.ModelSerializer):

    class Meta:
        model = Supplier
        fields = ['supplier_id', 'name', 'address', 'tel_no']

class FabricVariantSerializer(serializers.ModelSerializer):
    class Meta:
        model = FabricVariant
        fields = [
            'id',
            'color',
            'color_name',
            'total_yard',
            'available_yard',
            'price_per_yard',
            'fabric_definition'
        ]

    def create(self, validated_data):
        color_code = validated_data.get('color')
        validated_data['color_name'] = COLOR_MAP.get(color_code, color_code)
        return super().create(validated_data)

class FabricDefinitionSerializer(serializers.ModelSerializer):
    # Optionally include variants in the same response
    variants = FabricVariantSerializer(many=True, read_only=True)
    suppliers = SupplierSerializer(many=True, read_only=True)
    supplier_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = FabricDefinition
        fields = [
            'id',
            'fabric_name',
            'suppliers',
            'supplier_ids',
            'date_added',
            'variants'
        ]

    def create(self, validated_data):
        supplier_ids = validated_data.pop('supplier_ids', [])
        fabric_definition = FabricDefinition.objects.create(**validated_data)
        if supplier_ids:
            fabric_definition.suppliers.set(supplier_ids)
        return fabric_definition

    def update(self, instance, validated_data):
        supplier_ids = validated_data.pop('supplier_ids', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        if supplier_ids is not None:
            instance.suppliers.set(supplier_ids)
        return instance


class FabricDefinitionDetailSerializer(serializers.ModelSerializer):
    suppliers = SupplierSerializer(many=True, read_only=True)
    supplier_names = serializers.SerializerMethodField()
    variant_count = serializers.SerializerMethodField()
    supplier_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = FabricDefinition
        fields = ['id', 'fabric_name', 'suppliers', 'supplier_names', 'supplier_ids', 'date_added', 'variant_count']

    def get_variant_count(self, obj):
        return obj.variants.count()

    def get_supplier_names(self, obj):
        return [supplier.name for supplier in obj.suppliers.all()]

    def create(self, validated_data):
        supplier_ids = validated_data.pop('supplier_ids', [])
        fabric_definition = FabricDefinition.objects.create(**validated_data)
        if supplier_ids:
            fabric_definition.suppliers.set(supplier_ids)
        return fabric_definition

    def update(self, instance, validated_data):
        supplier_ids = validated_data.pop('supplier_ids', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        if supplier_ids is not None:
            instance.suppliers.set(supplier_ids)
        return instance
