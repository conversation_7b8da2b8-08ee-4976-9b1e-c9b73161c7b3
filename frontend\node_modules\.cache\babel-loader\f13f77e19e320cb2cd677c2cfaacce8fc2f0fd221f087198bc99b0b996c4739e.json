{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Pri_Fashion_\\\\frontend\\\\src\\\\pages\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { Container, Row, Col, Card, Form, Button, Alert, InputGroup, Spinner, Table, Badge, Modal } from 'react-bootstrap';\nimport { FaUser, FaLock, FaUserTag, FaSignInAlt, FaEye, FaEyeSlash, FaSearch, FaEdit, FaTrash } from 'react-icons/fa';\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\nimport { getUserRole } from '../utils/auth';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Signup() {\n  _s();\n  // State variables\n  const [username, setUsername] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\n  const [role, setRole] = useState(\"\");\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n  const [validated, setValidated] = useState(false);\n\n  // User list state variables\n  const [users, setUsers] = useState([]);\n  const [userListLoading, setUserListLoading] = useState(false);\n  const [userListError, setUserListError] = useState(\"\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [userRole, setUserRole] = useState(getUserRole());\n\n  // Edit user modal state\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [editUser, setEditUser] = useState(null);\n  const [editPassword, setEditPassword] = useState(\"\");\n  const [editConfirmPassword, setEditConfirmPassword] = useState(\"\");\n  const [editLoading, setEditLoading] = useState(false);\n  const [editError, setEditError] = useState(\"\");\n  const [editPasswordError, setEditPasswordError] = useState(\"\");\n\n  // Delete confirmation state\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [deleteUserId, setDeleteUserId] = useState(null);\n  const [deleteLoading, setDeleteLoading] = useState(false);\n\n  // Form validation errors\n  const [usernameError, setUsernameError] = useState(\"\");\n  const [passwordError, setPasswordError] = useState(\"\");\n  const [confirmPasswordError, setConfirmPasswordError] = useState(\"\");\n  const [roleError, setRoleError] = useState(\"\");\n\n  // Effect to handle sidebar state based on window size\n  useEffect(() => {\n    const handleResize = () => {\n      setIsSidebarOpen(window.innerWidth >= 768);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n\n  // Function to fetch users\n  const fetchUsers = async () => {\n    // Only fetch users if the current user is an Owner\n    if (userRole !== 'Owner') {\n      return;\n    }\n    setUserListLoading(true);\n    setUserListError(\"\");\n    try {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        setUserListError(\"You must be logged in to view users\");\n        setUserListLoading(false);\n        return;\n      }\n      const response = await axios.get('http://localhost:8000/api/auth/users/', {\n        headers: {\n          'Authorization': `JWT ${token}`\n        }\n      });\n      setUsers(response.data);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Error fetching users:\", error);\n      setUserListError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || \"Failed to load users. Please try again.\");\n    } finally {\n      setUserListLoading(false);\n    }\n  };\n\n  // Effect to load users when component mounts if user is Owner\n  useEffect(() => {\n    if (userRole === 'Owner') {\n      fetchUsers();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [userRole]);\n\n  // Validate username\n  const validateUsername = username => {\n    if (!username || username.trim() === \"\") {\n      setUsernameError(\"Username is required\");\n      return false;\n    } else if (username.length < 3) {\n      setUsernameError(\"Username must be at least 3 characters\");\n      return false;\n    }\n    setUsernameError(\"\");\n    return true;\n  };\n\n  // Validate password\n  const validatePassword = password => {\n    if (!password) {\n      setPasswordError(\"Password is required\");\n      return false;\n    } else if (password.length < 6) {\n      setPasswordError(\"Password must be at least 6 characters\");\n      return false;\n    }\n    setPasswordError(\"\");\n    return true;\n  };\n\n  // Validate confirm password\n  const validateConfirmPassword = confirmPassword => {\n    if (!confirmPassword) {\n      setConfirmPasswordError(\"Please confirm your password\");\n      return false;\n    } else if (confirmPassword !== password) {\n      setConfirmPasswordError(\"Passwords do not match\");\n      return false;\n    }\n    setConfirmPasswordError(\"\");\n    return true;\n  };\n\n  // Validate role\n  const validateRole = role => {\n    if (!role) {\n      setRoleError(\"Please select a role\");\n      return false;\n    }\n    setRoleError(\"\");\n    return true;\n  };\n\n  // Handle form submission\n  const handleSignup = async e => {\n    e.preventDefault();\n\n    // Reset messages\n    setError(\"\");\n    setSuccess(\"\");\n\n    // Validate form\n    const isUsernameValid = validateUsername(username);\n    const isPasswordValid = validatePassword(password);\n    const isConfirmPasswordValid = validateConfirmPassword(confirmPassword);\n    const isRoleValid = validateRole(role);\n    if (!isUsernameValid || !isPasswordValid || !isConfirmPasswordValid || !isRoleValid) {\n      setValidated(true);\n      return;\n    }\n    setLoading(true);\n    const userData = {\n      username,\n      password,\n      role\n    };\n    try {\n      const response = await axios.post('http://localhost:8000/api/auth/signup/', userData);\n      if (response && response.data) {\n        setSuccess(\"Registration successful! You can now log in.\");\n        // Reset form\n        setUsername(\"\");\n        setPassword(\"\");\n        setConfirmPassword(\"\");\n        setRole(\"\");\n        setValidated(false);\n\n        // Refresh user list if owner is logged in\n        if (userRole === 'Owner') {\n          fetchUsers();\n        }\n      } else {\n        setError(\"Unexpected response from server. Please try again.\");\n      }\n    } catch (error) {\n      if (error.response && error.response.data) {\n        setError(error.response.data.error || \"Registration failed. Please try again.\");\n      } else {\n        setError(\"Network error. Please check your connection and try again.\");\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Function to handle search\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n  };\n\n  // Filter users based on search term\n  const filteredUsers = users.filter(user => user.username.toLowerCase().includes(searchTerm.toLowerCase()) || user.role_name && user.role_name.toLowerCase().includes(searchTerm.toLowerCase()));\n\n  // Handle edit user (change password)\n  const handleEditUser = user => {\n    setEditUser(user);\n    setEditPassword(\"\");\n    setEditConfirmPassword(\"\");\n    setEditError(\"\");\n    setShowEditModal(true);\n  };\n\n  // Validate password\n  const validateEditPassword = password => {\n    if (!password) {\n      setEditError(\"Password is required\");\n      return false;\n    } else if (password.length < 6) {\n      setEditError(\"Password must be at least 6 characters\");\n      return false;\n    }\n    return true;\n  };\n\n  // Validate confirm password\n  const validateEditConfirmPassword = (password, confirmPassword) => {\n    if (!confirmPassword) {\n      setEditError(\"Please confirm your password\");\n      return false;\n    } else if (confirmPassword !== password) {\n      setEditError(\"Passwords do not match\");\n      return false;\n    }\n    return true;\n  };\n\n  // Handle save edited user (change password)\n  const handleSaveEdit = async () => {\n    // Validate passwords\n    if (!validateEditPassword(editPassword) || !validateEditConfirmPassword(editPassword, editConfirmPassword)) {\n      return;\n    }\n    setEditLoading(true);\n    setEditError(\"\");\n    try {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        setEditError(\"You must be logged in to change user password\");\n        setEditLoading(false);\n        return;\n      }\n      const userData = {\n        password: editPassword\n      };\n      await axios.put(`http://localhost:8000/api/auth/users/${editUser.id}/`, userData, {\n        headers: {\n          'Authorization': `JWT ${token}`\n        }\n      });\n\n      // Refresh user list\n      fetchUsers();\n      setShowEditModal(false);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error(\"Error updating user password:\", error);\n      setEditError(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || \"Failed to update password. Please try again.\");\n    } finally {\n      setEditLoading(false);\n    }\n  };\n\n  // Handle delete user\n  const handleDeleteUser = userId => {\n    setDeleteUserId(userId);\n    setShowDeleteModal(true);\n  };\n\n  // Handle confirm delete\n  const handleConfirmDelete = async () => {\n    setDeleteLoading(true);\n    try {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        setShowDeleteModal(false);\n        setDeleteLoading(false);\n        return;\n      }\n      await axios.delete(`http://localhost:8000/api/auth/users/${deleteUserId}/`, {\n        headers: {\n          'Authorization': `JWT ${token}`\n        }\n      });\n\n      // Refresh user list\n      fetchUsers();\n      setShowDeleteModal(false);\n    } catch (error) {\n      console.error(\"Error deleting user:\", error);\n    } finally {\n      setDeleteLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      style: {\n        marginLeft: isSidebarOpen ? \"200px\" : \"60px\",\n        transition: \"margin-left 0.3s ease\",\n        paddingTop: \"20px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        fluid: true,\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            md: 12,\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              className: \"shadow-sm border-0 animate-fade-in\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"fw-bold text-primary\",\n                    children: \"New Registration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: \"Create your account to access the system\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"danger\",\n                  className: \"mb-4 animate-fade-in\",\n                  children: error\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"success\",\n                  className: \"mb-4 animate-fade-in\",\n                  children: success\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form, {\n                  noValidate: true,\n                  validated: validated,\n                  onSubmit: handleSignup,\n                  children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Username\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n                      children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                        className: \"bg-light\",\n                        children: /*#__PURE__*/_jsxDEV(FaUser, {\n                          className: \"text-primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 363,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 362,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"text\",\n                        placeholder: \"Enter your username\",\n                        value: username,\n                        onChange: e => {\n                          setUsername(e.target.value);\n                          validateUsername(e.target.value);\n                        },\n                        isInvalid: !!usernameError,\n                        autoComplete: \"off\",\n                        required: true\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                        type: \"invalid\",\n                        children: usernameError || \"Username is required\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 384,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n                      children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                        className: \"bg-light\",\n                        children: /*#__PURE__*/_jsxDEV(FaLock, {\n                          className: \"text-primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 387,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 386,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: showPassword ? \"text\" : \"password\",\n                        placeholder: \"Enter your password\",\n                        value: password,\n                        onChange: e => {\n                          setPassword(e.target.value);\n                          validatePassword(e.target.value);\n                        },\n                        isInvalid: !!passwordError,\n                        autoComplete: \"new-password\",\n                        required: true\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 389,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"light\",\n                        onClick: () => setShowPassword(!showPassword),\n                        className: \"border\",\n                        children: showPassword ? /*#__PURE__*/_jsxDEV(FaEyeSlash, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 406,\n                          columnNumber: 43\n                        }, this) : /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 406,\n                          columnNumber: 60\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 401,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                        type: \"invalid\",\n                        children: passwordError || \"Password is required\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 408,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                      className: \"text-muted\",\n                      children: \"Password must be at least 6 characters long\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Confirm Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 418,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n                      children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                        className: \"bg-light\",\n                        children: /*#__PURE__*/_jsxDEV(FaLock, {\n                          className: \"text-primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 421,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 420,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: showConfirmPassword ? \"text\" : \"password\",\n                        placeholder: \"Confirm your password\",\n                        value: confirmPassword,\n                        onChange: e => {\n                          setConfirmPassword(e.target.value);\n                          validateConfirmPassword(e.target.value);\n                        },\n                        isInvalid: !!confirmPasswordError,\n                        required: true\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 423,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"light\",\n                        onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                        className: \"border\",\n                        children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(FaEyeSlash, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 439,\n                          columnNumber: 50\n                        }, this) : /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 439,\n                          columnNumber: 67\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 434,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                        type: \"invalid\",\n                        children: confirmPasswordError || \"Please confirm your password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 441,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Role\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n                      children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                        className: \"bg-light\",\n                        children: /*#__PURE__*/_jsxDEV(FaUserTag, {\n                          className: \"text-primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 451,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 450,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                        value: role,\n                        onChange: e => {\n                          setRole(e.target.value);\n                          validateRole(e.target.value);\n                        },\n                        isInvalid: !!roleError,\n                        required: true,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Role\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 462,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Owner\",\n                          children: \"Owner\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 463,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Inventory Manager\",\n                          children: \"Inventory Manager\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 464,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Sales Team\",\n                          children: \"Sales Team\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 465,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Order Coordinator\",\n                          children: \"Order Coordinator\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 466,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 453,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                        type: \"invalid\",\n                        children: roleError || \"Please select a role\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 468,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"primary\",\n                    type: \"submit\",\n                    className: \"w-100 py-2 mb-3 animate-button\",\n                    disabled: loading,\n                    children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                        as: \"span\",\n                        animation: \"border\",\n                        size: \"sm\",\n                        role: \"status\",\n                        \"aria-hidden\": \"true\",\n                        className: \"me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 482,\n                        columnNumber: 27\n                      }, this), \"Registering...\"]\n                    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(FaSignInAlt, {\n                        className: \"me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 494,\n                        columnNumber: 27\n                      }, this), \" Register\"]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this), userRole === 'Owner' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"mb-3\",\n                    children: \"User List\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: /*#__PURE__*/_jsxDEV(InputGroup, {\n                      children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                        children: /*#__PURE__*/_jsxDEV(FaSearch, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 507,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 506,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        placeholder: \"Search users...\",\n                        value: searchTerm,\n                        onChange: handleSearch\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 509,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 23\n                  }, this), userListLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      animation: \"border\",\n                      variant: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mt-2\",\n                      children: \"Loading users...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 25\n                  }, this) : userListError ? /*#__PURE__*/_jsxDEV(Alert, {\n                    variant: \"danger\",\n                    children: userListError\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Table, {\n                    hover: true,\n                    responsive: true,\n                    className: \"mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Username\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 528,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Role\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 529,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 530,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Joined\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 531,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Actions\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 532,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 527,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 526,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      children: filteredUsers.length > 0 ? filteredUsers.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: user.username\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 539,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: user.role_name === 'Owner' ? 'danger' : user.role_name === 'Inventory Manager' ? 'success' : user.role_name === 'Sales Team' ? 'info' : user.role_name === 'Order Coordinator' ? 'warning' : 'secondary',\n                            children: user.role_name || 'No Role'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 541,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 540,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: user.is_active ? 'success' : 'danger',\n                            children: user.is_active ? 'Active' : 'Inactive'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 552,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 551,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: new Date(user.date_joined).toLocaleDateString()\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 556,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: [/*#__PURE__*/_jsxDEV(Button, {\n                            variant: \"outline-primary\",\n                            size: \"sm\",\n                            className: \"me-2\",\n                            onClick: () => handleEditUser(user),\n                            children: [/*#__PURE__*/_jsxDEV(FaEdit, {\n                              className: \"me-1\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 564,\n                              columnNumber: 39\n                            }, this), \" Change Password\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 558,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Button, {\n                            variant: \"outline-danger\",\n                            size: \"sm\",\n                            onClick: () => handleDeleteUser(user.id),\n                            children: [/*#__PURE__*/_jsxDEV(FaTrash, {\n                              className: \"me-1\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 571,\n                              columnNumber: 39\n                            }, this), \" Delete\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 566,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 557,\n                          columnNumber: 35\n                        }, this)]\n                      }, user.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 538,\n                        columnNumber: 33\n                      }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: /*#__PURE__*/_jsxDEV(\"td\", {\n                          colSpan: \"5\",\n                          className: \"text-center\",\n                          children: searchTerm ? 'No users match your search' : 'No users found'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 578,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 577,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 535,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mt-3\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted small\",\n                children: [\"\\xA9 \", new Date().getFullYear(), \" Pri Fashion. All rights reserved.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showEditModal,\n      onHide: () => setShowEditModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Change Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [editError && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: editError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [editUser && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-3\",\n            children: [\"Changing password for user: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: editUser.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 45\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"New Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"password\",\n              value: editPassword,\n              onChange: e => setEditPassword(e.target.value),\n              placeholder: \"Enter new password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"Password must be at least 6 characters long\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Confirm New Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"password\",\n              value: editConfirmPassword,\n              onChange: e => setEditConfirmPassword(e.target.value),\n              placeholder: \"Confirm new password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowEditModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSaveEdit,\n          disabled: editLoading,\n          children: editLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              as: \"span\",\n              animation: \"border\",\n              size: \"sm\",\n              role: \"status\",\n              \"aria-hidden\": \"true\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 17\n            }, this), \"Saving...\"]\n          }, void 0, true) : \"Change Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 601,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showDeleteModal,\n      onHide: () => setShowDeleteModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Confirm Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 667,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: \"Are you sure you want to delete this user? This action cannot be undone.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowDeleteModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"danger\",\n          onClick: handleConfirmDelete,\n          disabled: deleteLoading,\n          children: deleteLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              as: \"span\",\n              animation: \"border\",\n              size: \"sm\",\n              role: \"status\",\n              \"aria-hidden\": \"true\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 17\n            }, this), \"Deleting...\"]\n          }, void 0, true) : \"Delete User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 673,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 666,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: \"true\",\n      children: `\n        .animate-fade-in {\n          animation: fadeIn 0.8s ease-out forwards;\n        }\n\n        .animate-button {\n          transition: transform 0.2s, box-shadow 0.2s;\n        }\n\n        .animate-button:hover:not(:disabled) {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n        }\n\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 700,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(Signup, \"D0+nG8BmquRvxCVxNfHFzadX/DU=\");\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["useState", "useEffect", "axios", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "InputGroup", "Spinner", "Table", "Badge", "Modal", "FaUser", "FaLock", "FaUserTag", "FaSignInAlt", "FaEye", "FaEyeSlash", "FaSearch", "FaEdit", "FaTrash", "RoleBasedNavBar", "getUserRole", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Signup", "_s", "username", "setUsername", "password", "setPassword", "confirmPassword", "setConfirmPassword", "role", "setRole", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "loading", "setLoading", "error", "setError", "success", "setSuccess", "validated", "setValidated", "users", "setUsers", "userListLoading", "setUserListLoading", "userListError", "setUserListError", "searchTerm", "setSearchTerm", "userRole", "setUserRole", "showEditModal", "setShowEditModal", "editUser", "setEditUser", "editPassword", "setEditPassword", "editConfirmPassword", "setEditConfirmPassword", "editLoading", "setEditLoading", "editError", "setEditError", "editPasswordError", "setEditPasswordError", "showDeleteModal", "setShowDeleteModal", "deleteUserId", "setDeleteUserId", "deleteLoading", "setDeleteLoading", "usernameError", "setUsernameError", "passwordError", "setPasswordError", "confirmPasswordError", "setConfirmPasswordError", "roleError", "setRoleError", "handleResize", "addEventListener", "removeEventListener", "fetchUsers", "token", "localStorage", "getItem", "response", "get", "headers", "data", "_error$response", "_error$response$data", "console", "validateUsername", "trim", "length", "validatePassword", "validateConfirmPassword", "validateRole", "handleSignup", "e", "preventDefault", "isUsernameValid", "isPasswordValid", "isConfirmPasswordValid", "isRoleValid", "userData", "post", "handleSearch", "target", "value", "filteredUsers", "filter", "user", "toLowerCase", "includes", "role_name", "handleEditUser", "validateEditPassword", "validateEditConfirmPassword", "handleSaveEdit", "put", "id", "_error$response2", "_error$response2$data", "handleDeleteUser", "userId", "handleConfirmDelete", "delete", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "style", "marginLeft", "transition", "paddingTop", "fluid", "md", "Body", "variant", "noValidate", "onSubmit", "Group", "Label", "Text", "Control", "type", "placeholder", "onChange", "isInvalid", "autoComplete", "required", "<PERSON><PERSON><PERSON>", "onClick", "Select", "disabled", "as", "animation", "size", "hover", "responsive", "map", "bg", "is_active", "Date", "date_joined", "toLocaleDateString", "colSpan", "getFullYear", "show", "onHide", "Header", "closeButton", "Title", "Footer", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Pri_Fashion_/frontend/src/pages/Signup.js"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { Container, Row, Col, Card, Form, Button, Alert, InputGroup, Spinner, Table, Badge, Modal } from 'react-bootstrap';\r\nimport { FaUser, FaLock, FaUserTag, FaSignInAlt, FaEye, FaEyeSlash, FaSearch, FaEdit, FaTrash } from 'react-icons/fa';\r\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\r\nimport { getUserRole } from '../utils/auth';\r\n\r\nfunction Signup() {\r\n  // State variables\r\n  const [username, setUsername] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\r\n  const [role, setRole] = useState(\"\");\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(\"\");\r\n  const [success, setSuccess] = useState(\"\");\r\n  const [validated, setValidated] = useState(false);\r\n\r\n  // User list state variables\r\n  const [users, setUsers] = useState([]);\r\n  const [userListLoading, setUserListLoading] = useState(false);\r\n  const [userListError, setUserListError] = useState(\"\");\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [userRole, setUserRole] = useState(getUserRole());\r\n\r\n  // Edit user modal state\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [editUser, setEditUser] = useState(null);\r\n  const [editPassword, setEditPassword] = useState(\"\");\r\n  const [editConfirmPassword, setEditConfirmPassword] = useState(\"\");\r\n  const [editLoading, setEditLoading] = useState(false);\r\n  const [editError, setEditError] = useState(\"\");\r\n  const [editPasswordError, setEditPasswordError] = useState(\"\");\r\n\r\n  // Delete confirmation state\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [deleteUserId, setDeleteUserId] = useState(null);\r\n  const [deleteLoading, setDeleteLoading] = useState(false);\r\n\r\n  // Form validation errors\r\n  const [usernameError, setUsernameError] = useState(\"\");\r\n  const [passwordError, setPasswordError] = useState(\"\");\r\n  const [confirmPasswordError, setConfirmPasswordError] = useState(\"\");\r\n  const [roleError, setRoleError] = useState(\"\");\r\n\r\n  // Effect to handle sidebar state based on window size\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Function to fetch users\r\n  const fetchUsers = async () => {\r\n    // Only fetch users if the current user is an Owner\r\n    if (userRole !== 'Owner') {\r\n      return;\r\n    }\r\n\r\n    setUserListLoading(true);\r\n    setUserListError(\"\");\r\n\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      if (!token) {\r\n        setUserListError(\"You must be logged in to view users\");\r\n        setUserListLoading(false);\r\n        return;\r\n      }\r\n\r\n      const response = await axios.get('http://localhost:8000/api/auth/users/', {\r\n        headers: {\r\n          'Authorization': `JWT ${token}`\r\n        }\r\n      });\r\n\r\n      setUsers(response.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching users:\", error);\r\n      setUserListError(\r\n        error.response?.data?.error ||\r\n        \"Failed to load users. Please try again.\"\r\n      );\r\n    } finally {\r\n      setUserListLoading(false);\r\n    }\r\n  };\r\n\r\n  // Effect to load users when component mounts if user is Owner\r\n  useEffect(() => {\r\n    if (userRole === 'Owner') {\r\n      fetchUsers();\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [userRole]);\r\n\r\n  // Validate username\r\n  const validateUsername = (username) => {\r\n    if (!username || username.trim() === \"\") {\r\n      setUsernameError(\"Username is required\");\r\n      return false;\r\n    } else if (username.length < 3) {\r\n      setUsernameError(\"Username must be at least 3 characters\");\r\n      return false;\r\n    }\r\n    setUsernameError(\"\");\r\n    return true;\r\n  };\r\n\r\n  // Validate password\r\n  const validatePassword = (password) => {\r\n    if (!password) {\r\n      setPasswordError(\"Password is required\");\r\n      return false;\r\n    } else if (password.length < 6) {\r\n      setPasswordError(\"Password must be at least 6 characters\");\r\n      return false;\r\n    }\r\n    setPasswordError(\"\");\r\n    return true;\r\n  };\r\n\r\n  // Validate confirm password\r\n  const validateConfirmPassword = (confirmPassword) => {\r\n    if (!confirmPassword) {\r\n      setConfirmPasswordError(\"Please confirm your password\");\r\n      return false;\r\n    } else if (confirmPassword !== password) {\r\n      setConfirmPasswordError(\"Passwords do not match\");\r\n      return false;\r\n    }\r\n    setConfirmPasswordError(\"\");\r\n    return true;\r\n  };\r\n\r\n  // Validate role\r\n  const validateRole = (role) => {\r\n    if (!role) {\r\n      setRoleError(\"Please select a role\");\r\n      return false;\r\n    }\r\n    setRoleError(\"\");\r\n    return true;\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSignup = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Reset messages\r\n    setError(\"\");\r\n    setSuccess(\"\");\r\n\r\n    // Validate form\r\n    const isUsernameValid = validateUsername(username);\r\n    const isPasswordValid = validatePassword(password);\r\n    const isConfirmPasswordValid = validateConfirmPassword(confirmPassword);\r\n    const isRoleValid = validateRole(role);\r\n\r\n    if (!isUsernameValid || !isPasswordValid || !isConfirmPasswordValid || !isRoleValid) {\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    const userData = { username, password, role };\r\n\r\n    try {\r\n      const response = await axios.post('http://localhost:8000/api/auth/signup/', userData);\r\n\r\n      if (response && response.data) {\r\n        setSuccess(\"Registration successful! You can now log in.\");\r\n        // Reset form\r\n        setUsername(\"\");\r\n        setPassword(\"\");\r\n        setConfirmPassword(\"\");\r\n        setRole(\"\");\r\n        setValidated(false);\r\n\r\n        // Refresh user list if owner is logged in\r\n        if (userRole === 'Owner') {\r\n          fetchUsers();\r\n        }\r\n      } else {\r\n        setError(\"Unexpected response from server. Please try again.\");\r\n      }\r\n    } catch (error) {\r\n      if (error.response && error.response.data) {\r\n        setError(error.response.data.error || \"Registration failed. Please try again.\");\r\n      } else {\r\n        setError(\"Network error. Please check your connection and try again.\");\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Function to handle search\r\n  const handleSearch = (e) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n\r\n  // Filter users based on search term\r\n  const filteredUsers = users.filter(user =>\r\n    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    (user.role_name && user.role_name.toLowerCase().includes(searchTerm.toLowerCase()))\r\n  );\r\n\r\n  // Handle edit user (change password)\r\n  const handleEditUser = (user) => {\r\n    setEditUser(user);\r\n    setEditPassword(\"\");\r\n    setEditConfirmPassword(\"\");\r\n    setEditError(\"\");\r\n    setShowEditModal(true);\r\n  };\r\n\r\n  // Validate password\r\n  const validateEditPassword = (password) => {\r\n    if (!password) {\r\n      setEditError(\"Password is required\");\r\n      return false;\r\n    } else if (password.length < 6) {\r\n      setEditError(\"Password must be at least 6 characters\");\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  // Validate confirm password\r\n  const validateEditConfirmPassword = (password, confirmPassword) => {\r\n    if (!confirmPassword) {\r\n      setEditError(\"Please confirm your password\");\r\n      return false;\r\n    } else if (confirmPassword !== password) {\r\n      setEditError(\"Passwords do not match\");\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  // Handle save edited user (change password)\r\n  const handleSaveEdit = async () => {\r\n    // Validate passwords\r\n    if (!validateEditPassword(editPassword) ||\r\n        !validateEditConfirmPassword(editPassword, editConfirmPassword)) {\r\n      return;\r\n    }\r\n\r\n    setEditLoading(true);\r\n    setEditError(\"\");\r\n\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      if (!token) {\r\n        setEditError(\"You must be logged in to change user password\");\r\n        setEditLoading(false);\r\n        return;\r\n      }\r\n\r\n      const userData = {\r\n        password: editPassword\r\n      };\r\n\r\n      await axios.put(`http://localhost:8000/api/auth/users/${editUser.id}/`, userData, {\r\n        headers: {\r\n          'Authorization': `JWT ${token}`\r\n        }\r\n      });\r\n\r\n      // Refresh user list\r\n      fetchUsers();\r\n      setShowEditModal(false);\r\n    } catch (error) {\r\n      console.error(\"Error updating user password:\", error);\r\n      setEditError(\r\n        error.response?.data?.error ||\r\n        \"Failed to update password. Please try again.\"\r\n      );\r\n    } finally {\r\n      setEditLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle delete user\r\n  const handleDeleteUser = (userId) => {\r\n    setDeleteUserId(userId);\r\n    setShowDeleteModal(true);\r\n  };\r\n\r\n  // Handle confirm delete\r\n  const handleConfirmDelete = async () => {\r\n    setDeleteLoading(true);\r\n\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      if (!token) {\r\n        setShowDeleteModal(false);\r\n        setDeleteLoading(false);\r\n        return;\r\n      }\r\n\r\n      await axios.delete(`http://localhost:8000/api/auth/users/${deleteUserId}/`, {\r\n        headers: {\r\n          'Authorization': `JWT ${token}`\r\n        }\r\n      });\r\n\r\n      // Refresh user list\r\n      fetchUsers();\r\n      setShowDeleteModal(false);\r\n    } catch (error) {\r\n      console.error(\"Error deleting user:\", error);\r\n    } finally {\r\n      setDeleteLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Sidebar */}\r\n      <RoleBasedNavBar />\r\n\r\n      {/* Main Content */}\r\n      <div className=\"main-content\" style={{\r\n        marginLeft: isSidebarOpen ? \"200px\" : \"60px\",\r\n        transition: \"margin-left 0.3s ease\",\r\n        paddingTop: \"20px\"\r\n      }}>\r\n        <Container fluid>\r\n          <Row>\r\n            <Col md={12}>\r\n              <Card className=\"shadow-sm border-0 animate-fade-in\">\r\n                <Card.Body className=\"p-4\">\r\n                  <div className=\"text-center mb-4\">\r\n                    <h2 className=\"fw-bold text-primary\">New Registration</h2>\r\n                    <p className=\"text-muted\">Create your account to access the system</p>\r\n                  </div>\r\n\r\n                  {error && (\r\n                    <Alert variant=\"danger\" className=\"mb-4 animate-fade-in\">\r\n                      {error}\r\n                    </Alert>\r\n                  )}\r\n\r\n                  {success && (\r\n                    <Alert variant=\"success\" className=\"mb-4 animate-fade-in\">\r\n                      {success}\r\n                    </Alert>\r\n                  )}\r\n\r\n                  <Form noValidate validated={validated} onSubmit={handleSignup}>\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label>Username</Form.Label>\r\n                      <InputGroup>\r\n                        <InputGroup.Text className=\"bg-light\">\r\n                          <FaUser className=\"text-primary\" />\r\n                        </InputGroup.Text>\r\n                        <Form.Control\r\n                          type=\"text\"\r\n                          placeholder=\"Enter your username\"\r\n                          value={username}\r\n                          onChange={(e) => {\r\n                            setUsername(e.target.value);\r\n                            validateUsername(e.target.value);\r\n                          }}\r\n                          isInvalid={!!usernameError}\r\n                          autoComplete=\"off\"\r\n                          required\r\n                        />\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                          {usernameError || \"Username is required\"}\r\n                        </Form.Control.Feedback>\r\n                      </InputGroup>\r\n                    </Form.Group>\r\n\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label>Password</Form.Label>\r\n                      <InputGroup>\r\n                        <InputGroup.Text className=\"bg-light\">\r\n                          <FaLock className=\"text-primary\" />\r\n                        </InputGroup.Text>\r\n                        <Form.Control\r\n                          type={showPassword ? \"text\" : \"password\"}\r\n                          placeholder=\"Enter your password\"\r\n                          value={password}\r\n                          onChange={(e) => {\r\n                            setPassword(e.target.value);\r\n                            validatePassword(e.target.value);\r\n                          }}\r\n                          isInvalid={!!passwordError}\r\n                          autoComplete=\"new-password\"\r\n                          required\r\n                        />\r\n                        <Button\r\n                          variant=\"light\"\r\n                          onClick={() => setShowPassword(!showPassword)}\r\n                          className=\"border\"\r\n                        >\r\n                          {showPassword ? <FaEyeSlash /> : <FaEye />}\r\n                        </Button>\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                          {passwordError || \"Password is required\"}\r\n                        </Form.Control.Feedback>\r\n                      </InputGroup>\r\n                      <Form.Text className=\"text-muted\">\r\n                        Password must be at least 6 characters long\r\n                      </Form.Text>\r\n                    </Form.Group>\r\n\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label>Confirm Password</Form.Label>\r\n                      <InputGroup>\r\n                        <InputGroup.Text className=\"bg-light\">\r\n                          <FaLock className=\"text-primary\" />\r\n                        </InputGroup.Text>\r\n                        <Form.Control\r\n                          type={showConfirmPassword ? \"text\" : \"password\"}\r\n                          placeholder=\"Confirm your password\"\r\n                          value={confirmPassword}\r\n                          onChange={(e) => {\r\n                            setConfirmPassword(e.target.value);\r\n                            validateConfirmPassword(e.target.value);\r\n                          }}\r\n                          isInvalid={!!confirmPasswordError}\r\n                          required\r\n                        />\r\n                        <Button\r\n                          variant=\"light\"\r\n                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                          className=\"border\"\r\n                        >\r\n                          {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}\r\n                        </Button>\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                          {confirmPasswordError || \"Please confirm your password\"}\r\n                        </Form.Control.Feedback>\r\n                      </InputGroup>\r\n                    </Form.Group>\r\n\r\n                    <Form.Group className=\"mb-4\">\r\n                      <Form.Label>Role</Form.Label>\r\n                      <InputGroup>\r\n                        <InputGroup.Text className=\"bg-light\">\r\n                          <FaUserTag className=\"text-primary\" />\r\n                        </InputGroup.Text>\r\n                        <Form.Select\r\n                          value={role}\r\n                          onChange={(e) => {\r\n                            setRole(e.target.value);\r\n                            validateRole(e.target.value);\r\n                          }}\r\n                          isInvalid={!!roleError}\r\n                          required\r\n                        >\r\n                          <option value=\"\">Select Role</option>\r\n                          <option value=\"Owner\">Owner</option>\r\n                          <option value=\"Inventory Manager\">Inventory Manager</option>\r\n                          <option value=\"Sales Team\">Sales Team</option>\r\n                          <option value=\"Order Coordinator\">Order Coordinator</option>\r\n                        </Form.Select>\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                          {roleError || \"Please select a role\"}\r\n                        </Form.Control.Feedback>\r\n                      </InputGroup>\r\n                    </Form.Group>\r\n\r\n                    <Button\r\n                      variant=\"primary\"\r\n                      type=\"submit\"\r\n                      className=\"w-100 py-2 mb-3 animate-button\"\r\n                      disabled={loading}\r\n                    >\r\n                      {loading ? (\r\n                        <>\r\n                          <Spinner\r\n                            as=\"span\"\r\n                            animation=\"border\"\r\n                            size=\"sm\"\r\n                            role=\"status\"\r\n                            aria-hidden=\"true\"\r\n                            className=\"me-2\"\r\n                          />\r\n                          Registering...\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <FaSignInAlt className=\"me-2\" /> Register\r\n                        </>\r\n                      )}\r\n                    </Button>\r\n                  </Form>\r\n\r\n                  {/* User List Section - Only visible to Owners */}\r\n                  {userRole === 'Owner' && (\r\n                    <div className=\"mt-5\">\r\n                      <h4 className=\"mb-3\">User List</h4>\r\n                      <div className=\"mb-3\">\r\n                        <InputGroup>\r\n                          <InputGroup.Text>\r\n                            <FaSearch />\r\n                          </InputGroup.Text>\r\n                          <Form.Control\r\n                            placeholder=\"Search users...\"\r\n                            value={searchTerm}\r\n                            onChange={handleSearch}\r\n                          />\r\n                        </InputGroup>\r\n                      </div>\r\n\r\n                      {userListLoading ? (\r\n                        <div className=\"text-center p-4\">\r\n                          <Spinner animation=\"border\" variant=\"primary\" />\r\n                          <p className=\"mt-2\">Loading users...</p>\r\n                        </div>\r\n                      ) : userListError ? (\r\n                        <Alert variant=\"danger\">{userListError}</Alert>\r\n                      ) : (\r\n                        <Table hover responsive className=\"mb-0\">\r\n                          <thead>\r\n                            <tr>\r\n                              <th>Username</th>\r\n                              <th>Role</th>\r\n                              <th>Status</th>\r\n                              <th>Joined</th>\r\n                              <th>Actions</th>\r\n                            </tr>\r\n                          </thead>\r\n                          <tbody>\r\n                            {filteredUsers.length > 0 ? (\r\n                              filteredUsers.map(user => (\r\n                                <tr key={user.id}>\r\n                                  <td>{user.username}</td>\r\n                                  <td>\r\n                                    <Badge bg={\r\n                                      user.role_name === 'Owner' ? 'danger' :\r\n                                      user.role_name === 'Inventory Manager' ? 'success' :\r\n                                      user.role_name === 'Sales Team' ? 'info' :\r\n                                      user.role_name === 'Order Coordinator' ? 'warning' :\r\n                                      'secondary'\r\n                                    }>\r\n                                      {user.role_name || 'No Role'}\r\n                                    </Badge>\r\n                                  </td>\r\n                                  <td>\r\n                                    <Badge bg={user.is_active ? 'success' : 'danger'}>\r\n                                      {user.is_active ? 'Active' : 'Inactive'}\r\n                                    </Badge>\r\n                                  </td>\r\n                                  <td>{new Date(user.date_joined).toLocaleDateString()}</td>\r\n                                  <td>\r\n                                    <Button\r\n                                      variant=\"outline-primary\"\r\n                                      size=\"sm\"\r\n                                      className=\"me-2\"\r\n                                      onClick={() => handleEditUser(user)}\r\n                                    >\r\n                                      <FaEdit className=\"me-1\" /> Change Password\r\n                                    </Button>\r\n                                    <Button\r\n                                      variant=\"outline-danger\"\r\n                                      size=\"sm\"\r\n                                      onClick={() => handleDeleteUser(user.id)}\r\n                                    >\r\n                                      <FaTrash className=\"me-1\" /> Delete\r\n                                    </Button>\r\n                                  </td>\r\n                                </tr>\r\n                              ))\r\n                            ) : (\r\n                              <tr>\r\n                                <td colSpan=\"5\" className=\"text-center\">\r\n                                  {searchTerm ? 'No users match your search' : 'No users found'}\r\n                                </td>\r\n                              </tr>\r\n                            )}\r\n                          </tbody>\r\n                        </Table>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n                </Card.Body>\r\n              </Card>\r\n              <div className=\"text-center mt-3\">\r\n                <p className=\"text-muted small\">\r\n                  &copy; {new Date().getFullYear()} Pri Fashion. All rights reserved.\r\n                </p>\r\n              </div>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n      </div>\r\n\r\n      {/* Change Password Modal */}\r\n      <Modal show={showEditModal} onHide={() => setShowEditModal(false)}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Change Password</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {editError && <Alert variant=\"danger\">{editError}</Alert>}\r\n\r\n          <Form>\r\n            {editUser && (\r\n              <p className=\"mb-3\">\r\n                Changing password for user: <strong>{editUser.username}</strong>\r\n              </p>\r\n            )}\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>New Password</Form.Label>\r\n              <Form.Control\r\n                type=\"password\"\r\n                value={editPassword}\r\n                onChange={(e) => setEditPassword(e.target.value)}\r\n                placeholder=\"Enter new password\"\r\n              />\r\n              <Form.Text className=\"text-muted\">\r\n                Password must be at least 6 characters long\r\n              </Form.Text>\r\n            </Form.Group>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Confirm New Password</Form.Label>\r\n              <Form.Control\r\n                type=\"password\"\r\n                value={editConfirmPassword}\r\n                onChange={(e) => setEditConfirmPassword(e.target.value)}\r\n                placeholder=\"Confirm new password\"\r\n              />\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowEditModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={handleSaveEdit}\r\n            disabled={editLoading}\r\n          >\r\n            {editLoading ? (\r\n              <>\r\n                <Spinner\r\n                  as=\"span\"\r\n                  animation=\"border\"\r\n                  size=\"sm\"\r\n                  role=\"status\"\r\n                  aria-hidden=\"true\"\r\n                  className=\"me-2\"\r\n                />\r\n                Saving...\r\n              </>\r\n            ) : \"Change Password\"}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Confirm Delete</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          Are you sure you want to delete this user? This action cannot be undone.\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowDeleteModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"danger\"\r\n            onClick={handleConfirmDelete}\r\n            disabled={deleteLoading}\r\n          >\r\n            {deleteLoading ? (\r\n              <>\r\n                <Spinner\r\n                  as=\"span\"\r\n                  animation=\"border\"\r\n                  size=\"sm\"\r\n                  role=\"status\"\r\n                  aria-hidden=\"true\"\r\n                  className=\"me-2\"\r\n                />\r\n                Deleting...\r\n              </>\r\n            ) : \"Delete User\"}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Custom CSS with animations */}\r\n      <style jsx=\"true\">{`\r\n        .animate-fade-in {\r\n          animation: fadeIn 0.8s ease-out forwards;\r\n        }\r\n\r\n        .animate-button {\r\n          transition: transform 0.2s, box-shadow 0.2s;\r\n        }\r\n\r\n        .animate-button:hover:not(:disabled) {\r\n          transform: translateY(-2px);\r\n          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\r\n        }\r\n\r\n        @keyframes fadeIn {\r\n          from { opacity: 0; transform: translateY(10px); }\r\n          to { opacity: 1; transform: translateY(0); }\r\n        }\r\n      `}</style>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Signup;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AAC1H,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AACrH,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,WAAW,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsC,IAAI,EAAEC,OAAO,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC8C,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAC5E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsD,SAAS,EAAEC,YAAY,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAACwD,KAAK,EAAEC,QAAQ,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAACyB,WAAW,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoE,QAAQ,EAAEC,WAAW,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4E,SAAS,EAAEC,YAAY,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;;EAE9D;EACA,MAAM,CAACgF,eAAe,EAAEC,kBAAkB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkF,YAAY,EAAEC,eAAe,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoF,aAAa,EAAEC,gBAAgB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACsF,aAAa,EAAEC,gBAAgB,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwF,aAAa,EAAEC,gBAAgB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0F,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC4F,SAAS,EAAEC,YAAY,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6F,YAAY,GAAGA,CAAA,KAAM;MACzBjD,gBAAgB,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5C,CAAC;IAEDD,MAAM,CAACiD,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMhD,MAAM,CAACkD,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B;IACA,IAAIjC,QAAQ,KAAK,OAAO,EAAE;MACxB;IACF;IAEAL,kBAAkB,CAAC,IAAI,CAAC;IACxBE,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAI;MACF,MAAMqC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACVrC,gBAAgB,CAAC,qCAAqC,CAAC;QACvDF,kBAAkB,CAAC,KAAK,CAAC;QACzB;MACF;MAEA,MAAM0C,QAAQ,GAAG,MAAMnG,KAAK,CAACoG,GAAG,CAAC,uCAAuC,EAAE;QACxEC,OAAO,EAAE;UACP,eAAe,EAAE,OAAOL,KAAK;QAC/B;MACF,CAAC,CAAC;MAEFzC,QAAQ,CAAC4C,QAAQ,CAACG,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOtD,KAAK,EAAE;MAAA,IAAAuD,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACzD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CW,gBAAgB,CACd,EAAA4C,eAAA,GAAAvD,KAAK,CAACmD,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBD,IAAI,cAAAE,oBAAA,uBAApBA,oBAAA,CAAsBxD,KAAK,KAC3B,yCACF,CAAC;IACH,CAAC,SAAS;MACRS,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA1D,SAAS,CAAC,MAAM;IACd,IAAI+D,QAAQ,KAAK,OAAO,EAAE;MACxBiC,UAAU,CAAC,CAAC;IACd;IACA;EACF,CAAC,EAAE,CAACjC,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM4C,gBAAgB,GAAI5E,QAAQ,IAAK;IACrC,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAAC6E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACvCtB,gBAAgB,CAAC,sBAAsB,CAAC;MACxC,OAAO,KAAK;IACd,CAAC,MAAM,IAAIvD,QAAQ,CAAC8E,MAAM,GAAG,CAAC,EAAE;MAC9BvB,gBAAgB,CAAC,wCAAwC,CAAC;MAC1D,OAAO,KAAK;IACd;IACAA,gBAAgB,CAAC,EAAE,CAAC;IACpB,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMwB,gBAAgB,GAAI7E,QAAQ,IAAK;IACrC,IAAI,CAACA,QAAQ,EAAE;MACbuD,gBAAgB,CAAC,sBAAsB,CAAC;MACxC,OAAO,KAAK;IACd,CAAC,MAAM,IAAIvD,QAAQ,CAAC4E,MAAM,GAAG,CAAC,EAAE;MAC9BrB,gBAAgB,CAAC,wCAAwC,CAAC;MAC1D,OAAO,KAAK;IACd;IACAA,gBAAgB,CAAC,EAAE,CAAC;IACpB,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMuB,uBAAuB,GAAI5E,eAAe,IAAK;IACnD,IAAI,CAACA,eAAe,EAAE;MACpBuD,uBAAuB,CAAC,8BAA8B,CAAC;MACvD,OAAO,KAAK;IACd,CAAC,MAAM,IAAIvD,eAAe,KAAKF,QAAQ,EAAE;MACvCyD,uBAAuB,CAAC,wBAAwB,CAAC;MACjD,OAAO,KAAK;IACd;IACAA,uBAAuB,CAAC,EAAE,CAAC;IAC3B,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMsB,YAAY,GAAI3E,IAAI,IAAK;IAC7B,IAAI,CAACA,IAAI,EAAE;MACTuD,YAAY,CAAC,sBAAsB,CAAC;MACpC,OAAO,KAAK;IACd;IACAA,YAAY,CAAC,EAAE,CAAC;IAChB,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMqB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACAjE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;;IAEd;IACA,MAAMgE,eAAe,GAAGT,gBAAgB,CAAC5E,QAAQ,CAAC;IAClD,MAAMsF,eAAe,GAAGP,gBAAgB,CAAC7E,QAAQ,CAAC;IAClD,MAAMqF,sBAAsB,GAAGP,uBAAuB,CAAC5E,eAAe,CAAC;IACvE,MAAMoF,WAAW,GAAGP,YAAY,CAAC3E,IAAI,CAAC;IAEtC,IAAI,CAAC+E,eAAe,IAAI,CAACC,eAAe,IAAI,CAACC,sBAAsB,IAAI,CAACC,WAAW,EAAE;MACnFjE,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;IAEAN,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMwE,QAAQ,GAAG;MAAEzF,QAAQ;MAAEE,QAAQ;MAAEI;IAAK,CAAC;IAE7C,IAAI;MACF,MAAM+D,QAAQ,GAAG,MAAMnG,KAAK,CAACwH,IAAI,CAAC,wCAAwC,EAAED,QAAQ,CAAC;MAErF,IAAIpB,QAAQ,IAAIA,QAAQ,CAACG,IAAI,EAAE;QAC7BnD,UAAU,CAAC,8CAA8C,CAAC;QAC1D;QACApB,WAAW,CAAC,EAAE,CAAC;QACfE,WAAW,CAAC,EAAE,CAAC;QACfE,kBAAkB,CAAC,EAAE,CAAC;QACtBE,OAAO,CAAC,EAAE,CAAC;QACXgB,YAAY,CAAC,KAAK,CAAC;;QAEnB;QACA,IAAIS,QAAQ,KAAK,OAAO,EAAE;UACxBiC,UAAU,CAAC,CAAC;QACd;MACF,CAAC,MAAM;QACL9C,QAAQ,CAAC,oDAAoD,CAAC;MAChE;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACd,IAAIA,KAAK,CAACmD,QAAQ,IAAInD,KAAK,CAACmD,QAAQ,CAACG,IAAI,EAAE;QACzCrD,QAAQ,CAACD,KAAK,CAACmD,QAAQ,CAACG,IAAI,CAACtD,KAAK,IAAI,wCAAwC,CAAC;MACjF,CAAC,MAAM;QACLC,QAAQ,CAAC,4DAA4D,CAAC;MACxE;IACF,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0E,YAAY,GAAIR,CAAC,IAAK;IAC1BpD,aAAa,CAACoD,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGtE,KAAK,CAACuE,MAAM,CAACC,IAAI,IACrCA,IAAI,CAAChG,QAAQ,CAACiG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpE,UAAU,CAACmE,WAAW,CAAC,CAAC,CAAC,IAC7DD,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACG,SAAS,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpE,UAAU,CAACmE,WAAW,CAAC,CAAC,CACnF,CAAC;;EAED;EACA,MAAMG,cAAc,GAAIJ,IAAI,IAAK;IAC/B3D,WAAW,CAAC2D,IAAI,CAAC;IACjBzD,eAAe,CAAC,EAAE,CAAC;IACnBE,sBAAsB,CAAC,EAAE,CAAC;IAC1BI,YAAY,CAAC,EAAE,CAAC;IAChBV,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMkE,oBAAoB,GAAInG,QAAQ,IAAK;IACzC,IAAI,CAACA,QAAQ,EAAE;MACb2C,YAAY,CAAC,sBAAsB,CAAC;MACpC,OAAO,KAAK;IACd,CAAC,MAAM,IAAI3C,QAAQ,CAAC4E,MAAM,GAAG,CAAC,EAAE;MAC9BjC,YAAY,CAAC,wCAAwC,CAAC;MACtD,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMyD,2BAA2B,GAAGA,CAACpG,QAAQ,EAAEE,eAAe,KAAK;IACjE,IAAI,CAACA,eAAe,EAAE;MACpByC,YAAY,CAAC,8BAA8B,CAAC;MAC5C,OAAO,KAAK;IACd,CAAC,MAAM,IAAIzC,eAAe,KAAKF,QAAQ,EAAE;MACvC2C,YAAY,CAAC,wBAAwB,CAAC;MACtC,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM0D,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC;IACA,IAAI,CAACF,oBAAoB,CAAC/D,YAAY,CAAC,IACnC,CAACgE,2BAA2B,CAAChE,YAAY,EAAEE,mBAAmB,CAAC,EAAE;MACnE;IACF;IAEAG,cAAc,CAAC,IAAI,CAAC;IACpBE,YAAY,CAAC,EAAE,CAAC;IAEhB,IAAI;MACF,MAAMqB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACVrB,YAAY,CAAC,+CAA+C,CAAC;QAC7DF,cAAc,CAAC,KAAK,CAAC;QACrB;MACF;MAEA,MAAM8C,QAAQ,GAAG;QACfvF,QAAQ,EAAEoC;MACZ,CAAC;MAED,MAAMpE,KAAK,CAACsI,GAAG,CAAC,wCAAwCpE,QAAQ,CAACqE,EAAE,GAAG,EAAEhB,QAAQ,EAAE;QAChFlB,OAAO,EAAE;UACP,eAAe,EAAE,OAAOL,KAAK;QAC/B;MACF,CAAC,CAAC;;MAEF;MACAD,UAAU,CAAC,CAAC;MACZ9B,gBAAgB,CAAC,KAAK,CAAC;IACzB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MAAA,IAAAwF,gBAAA,EAAAC,qBAAA;MACdhC,OAAO,CAACzD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD2B,YAAY,CACV,EAAA6D,gBAAA,GAAAxF,KAAK,CAACmD,QAAQ,cAAAqC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlC,IAAI,cAAAmC,qBAAA,uBAApBA,qBAAA,CAAsBzF,KAAK,KAC3B,8CACF,CAAC;IACH,CAAC,SAAS;MACRyB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMiE,gBAAgB,GAAIC,MAAM,IAAK;IACnC1D,eAAe,CAAC0D,MAAM,CAAC;IACvB5D,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM6D,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCzD,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACF,MAAMa,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACVjB,kBAAkB,CAAC,KAAK,CAAC;QACzBI,gBAAgB,CAAC,KAAK,CAAC;QACvB;MACF;MAEA,MAAMnF,KAAK,CAAC6I,MAAM,CAAC,wCAAwC7D,YAAY,GAAG,EAAE;QAC1EqB,OAAO,EAAE;UACP,eAAe,EAAE,OAAOL,KAAK;QAC/B;MACF,CAAC,CAAC;;MAEF;MACAD,UAAU,CAAC,CAAC;MACZhB,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdyD,OAAO,CAACzD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRmC,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,oBACE1D,OAAA,CAAAE,SAAA;IAAAmH,QAAA,gBAEErH,OAAA,CAACH,eAAe;MAAAyH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnBzH,OAAA;MAAK0H,SAAS,EAAC,cAAc;MAACC,KAAK,EAAE;QACnCC,UAAU,EAAE3G,aAAa,GAAG,OAAO,GAAG,MAAM;QAC5C4G,UAAU,EAAE,uBAAuB;QACnCC,UAAU,EAAE;MACd,CAAE;MAAAT,QAAA,eACArH,OAAA,CAACxB,SAAS;QAACuJ,KAAK;QAAAV,QAAA,eACdrH,OAAA,CAACvB,GAAG;UAAA4I,QAAA,eACFrH,OAAA,CAACtB,GAAG;YAACsJ,EAAE,EAAE,EAAG;YAAAX,QAAA,gBACVrH,OAAA,CAACrB,IAAI;cAAC+I,SAAS,EAAC,oCAAoC;cAAAL,QAAA,eAClDrH,OAAA,CAACrB,IAAI,CAACsJ,IAAI;gBAACP,SAAS,EAAC,KAAK;gBAAAL,QAAA,gBACxBrH,OAAA;kBAAK0H,SAAS,EAAC,kBAAkB;kBAAAL,QAAA,gBAC/BrH,OAAA;oBAAI0H,SAAS,EAAC,sBAAsB;oBAAAL,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1DzH,OAAA;oBAAG0H,SAAS,EAAC,YAAY;oBAAAL,QAAA,EAAC;kBAAwC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,EAELlG,KAAK,iBACJvB,OAAA,CAAClB,KAAK;kBAACoJ,OAAO,EAAC,QAAQ;kBAACR,SAAS,EAAC,sBAAsB;kBAAAL,QAAA,EACrD9F;gBAAK;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACR,EAEAhG,OAAO,iBACNzB,OAAA,CAAClB,KAAK;kBAACoJ,OAAO,EAAC,SAAS;kBAACR,SAAS,EAAC,sBAAsB;kBAAAL,QAAA,EACtD5F;gBAAO;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACR,eAEDzH,OAAA,CAACpB,IAAI;kBAACuJ,UAAU;kBAACxG,SAAS,EAAEA,SAAU;kBAACyG,QAAQ,EAAE7C,YAAa;kBAAA8B,QAAA,gBAC5DrH,OAAA,CAACpB,IAAI,CAACyJ,KAAK;oBAACX,SAAS,EAAC,MAAM;oBAAAL,QAAA,gBAC1BrH,OAAA,CAACpB,IAAI,CAAC0J,KAAK;sBAAAjB,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjCzH,OAAA,CAACjB,UAAU;sBAAAsI,QAAA,gBACTrH,OAAA,CAACjB,UAAU,CAACwJ,IAAI;wBAACb,SAAS,EAAC,UAAU;wBAAAL,QAAA,eACnCrH,OAAA,CAACZ,MAAM;0BAACsI,SAAS,EAAC;wBAAc;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAClBzH,OAAA,CAACpB,IAAI,CAAC4J,OAAO;wBACXC,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,qBAAqB;wBACjCxC,KAAK,EAAE7F,QAAS;wBAChBsI,QAAQ,EAAGnD,CAAC,IAAK;0BACflF,WAAW,CAACkF,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;0BAC3BjB,gBAAgB,CAACO,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;wBAClC,CAAE;wBACF0C,SAAS,EAAE,CAAC,CAACjF,aAAc;wBAC3BkF,YAAY,EAAC,KAAK;wBAClBC,QAAQ;sBAAA;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC,eACFzH,OAAA,CAACpB,IAAI,CAAC4J,OAAO,CAACO,QAAQ;wBAACN,IAAI,EAAC,SAAS;wBAAApB,QAAA,EAClC1D,aAAa,IAAI;sBAAsB;wBAAA2D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEbzH,OAAA,CAACpB,IAAI,CAACyJ,KAAK;oBAACX,SAAS,EAAC,MAAM;oBAAAL,QAAA,gBAC1BrH,OAAA,CAACpB,IAAI,CAAC0J,KAAK;sBAAAjB,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjCzH,OAAA,CAACjB,UAAU;sBAAAsI,QAAA,gBACTrH,OAAA,CAACjB,UAAU,CAACwJ,IAAI;wBAACb,SAAS,EAAC,UAAU;wBAAAL,QAAA,eACnCrH,OAAA,CAACX,MAAM;0BAACqI,SAAS,EAAC;wBAAc;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAClBzH,OAAA,CAACpB,IAAI,CAAC4J,OAAO;wBACXC,IAAI,EAAE5H,YAAY,GAAG,MAAM,GAAG,UAAW;wBACzC6H,WAAW,EAAC,qBAAqB;wBACjCxC,KAAK,EAAE3F,QAAS;wBAChBoI,QAAQ,EAAGnD,CAAC,IAAK;0BACfhF,WAAW,CAACgF,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;0BAC3Bd,gBAAgB,CAACI,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;wBAClC,CAAE;wBACF0C,SAAS,EAAE,CAAC,CAAC/E,aAAc;wBAC3BgF,YAAY,EAAC,cAAc;wBAC3BC,QAAQ;sBAAA;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC,eACFzH,OAAA,CAACnB,MAAM;wBACLqJ,OAAO,EAAC,OAAO;wBACfc,OAAO,EAAEA,CAAA,KAAMlI,eAAe,CAAC,CAACD,YAAY,CAAE;wBAC9C6G,SAAS,EAAC,QAAQ;wBAAAL,QAAA,EAEjBxG,YAAY,gBAAGb,OAAA,CAACP,UAAU;0BAAA6H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGzH,OAAA,CAACR,KAAK;0BAAA8H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC,CAAC,eACTzH,OAAA,CAACpB,IAAI,CAAC4J,OAAO,CAACO,QAAQ;wBAACN,IAAI,EAAC,SAAS;wBAAApB,QAAA,EAClCxD,aAAa,IAAI;sBAAsB;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC,eACbzH,OAAA,CAACpB,IAAI,CAAC2J,IAAI;sBAACb,SAAS,EAAC,YAAY;sBAAAL,QAAA,EAAC;oBAElC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAEbzH,OAAA,CAACpB,IAAI,CAACyJ,KAAK;oBAACX,SAAS,EAAC,MAAM;oBAAAL,QAAA,gBAC1BrH,OAAA,CAACpB,IAAI,CAAC0J,KAAK;sBAAAjB,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzCzH,OAAA,CAACjB,UAAU;sBAAAsI,QAAA,gBACTrH,OAAA,CAACjB,UAAU,CAACwJ,IAAI;wBAACb,SAAS,EAAC,UAAU;wBAAAL,QAAA,eACnCrH,OAAA,CAACX,MAAM;0BAACqI,SAAS,EAAC;wBAAc;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eAClBzH,OAAA,CAACpB,IAAI,CAAC4J,OAAO;wBACXC,IAAI,EAAE1H,mBAAmB,GAAG,MAAM,GAAG,UAAW;wBAChD2H,WAAW,EAAC,uBAAuB;wBACnCxC,KAAK,EAAEzF,eAAgB;wBACvBkI,QAAQ,EAAGnD,CAAC,IAAK;0BACf9E,kBAAkB,CAAC8E,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;0BAClCb,uBAAuB,CAACG,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;wBACzC,CAAE;wBACF0C,SAAS,EAAE,CAAC,CAAC7E,oBAAqB;wBAClC+E,QAAQ;sBAAA;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC,eACFzH,OAAA,CAACnB,MAAM;wBACLqJ,OAAO,EAAC,OAAO;wBACfc,OAAO,EAAEA,CAAA,KAAMhI,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;wBAC5D2G,SAAS,EAAC,QAAQ;wBAAAL,QAAA,EAEjBtG,mBAAmB,gBAAGf,OAAA,CAACP,UAAU;0BAAA6H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGzH,OAAA,CAACR,KAAK;0BAAA8H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACTzH,OAAA,CAACpB,IAAI,CAAC4J,OAAO,CAACO,QAAQ;wBAACN,IAAI,EAAC,SAAS;wBAAApB,QAAA,EAClCtD,oBAAoB,IAAI;sBAA8B;wBAAAuD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEbzH,OAAA,CAACpB,IAAI,CAACyJ,KAAK;oBAACX,SAAS,EAAC,MAAM;oBAAAL,QAAA,gBAC1BrH,OAAA,CAACpB,IAAI,CAAC0J,KAAK;sBAAAjB,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC7BzH,OAAA,CAACjB,UAAU;sBAAAsI,QAAA,gBACTrH,OAAA,CAACjB,UAAU,CAACwJ,IAAI;wBAACb,SAAS,EAAC,UAAU;wBAAAL,QAAA,eACnCrH,OAAA,CAACV,SAAS;0BAACoI,SAAS,EAAC;wBAAc;0BAAAJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CAAC,eAClBzH,OAAA,CAACpB,IAAI,CAACqK,MAAM;wBACV/C,KAAK,EAAEvF,IAAK;wBACZgI,QAAQ,EAAGnD,CAAC,IAAK;0BACf5E,OAAO,CAAC4E,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;0BACvBZ,YAAY,CAACE,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC;wBAC9B,CAAE;wBACF0C,SAAS,EAAE,CAAC,CAAC3E,SAAU;wBACvB6E,QAAQ;wBAAAzB,QAAA,gBAERrH,OAAA;0BAAQkG,KAAK,EAAC,EAAE;0BAAAmB,QAAA,EAAC;wBAAW;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACrCzH,OAAA;0BAAQkG,KAAK,EAAC,OAAO;0BAAAmB,QAAA,EAAC;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpCzH,OAAA;0BAAQkG,KAAK,EAAC,mBAAmB;0BAAAmB,QAAA,EAAC;wBAAiB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5DzH,OAAA;0BAAQkG,KAAK,EAAC,YAAY;0BAAAmB,QAAA,EAAC;wBAAU;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC9CzH,OAAA;0BAAQkG,KAAK,EAAC,mBAAmB;0BAAAmB,QAAA,EAAC;wBAAiB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjD,CAAC,eACdzH,OAAA,CAACpB,IAAI,CAAC4J,OAAO,CAACO,QAAQ;wBAACN,IAAI,EAAC,SAAS;wBAAApB,QAAA,EAClCpD,SAAS,IAAI;sBAAsB;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEbzH,OAAA,CAACnB,MAAM;oBACLqJ,OAAO,EAAC,SAAS;oBACjBO,IAAI,EAAC,QAAQ;oBACbf,SAAS,EAAC,gCAAgC;oBAC1CwB,QAAQ,EAAE7H,OAAQ;oBAAAgG,QAAA,EAEjBhG,OAAO,gBACNrB,OAAA,CAAAE,SAAA;sBAAAmH,QAAA,gBACErH,OAAA,CAAChB,OAAO;wBACNmK,EAAE,EAAC,MAAM;wBACTC,SAAS,EAAC,QAAQ;wBAClBC,IAAI,EAAC,IAAI;wBACT1I,IAAI,EAAC,QAAQ;wBACb,eAAY,MAAM;wBAClB+G,SAAS,EAAC;sBAAM;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,kBAEJ;oBAAA,eAAE,CAAC,gBAEHzH,OAAA,CAAAE,SAAA;sBAAAmH,QAAA,gBACErH,OAAA,CAACT,WAAW;wBAACmI,SAAS,EAAC;sBAAM;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAClC;oBAAA,eAAE;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EAGNpF,QAAQ,KAAK,OAAO,iBACnBrC,OAAA;kBAAK0H,SAAS,EAAC,MAAM;kBAAAL,QAAA,gBACnBrH,OAAA;oBAAI0H,SAAS,EAAC,MAAM;oBAAAL,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnCzH,OAAA;oBAAK0H,SAAS,EAAC,MAAM;oBAAAL,QAAA,eACnBrH,OAAA,CAACjB,UAAU;sBAAAsI,QAAA,gBACTrH,OAAA,CAACjB,UAAU,CAACwJ,IAAI;wBAAAlB,QAAA,eACdrH,OAAA,CAACN,QAAQ;0BAAA4H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC,eAClBzH,OAAA,CAACpB,IAAI,CAAC4J,OAAO;wBACXE,WAAW,EAAC,iBAAiB;wBAC7BxC,KAAK,EAAE/D,UAAW;wBAClBwG,QAAQ,EAAE3C;sBAAa;wBAAAsB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,EAEL1F,eAAe,gBACd/B,OAAA;oBAAK0H,SAAS,EAAC,iBAAiB;oBAAAL,QAAA,gBAC9BrH,OAAA,CAAChB,OAAO;sBAACoK,SAAS,EAAC,QAAQ;sBAAClB,OAAO,EAAC;oBAAS;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChDzH,OAAA;sBAAG0H,SAAS,EAAC,MAAM;sBAAAL,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,GACJxF,aAAa,gBACfjC,OAAA,CAAClB,KAAK;oBAACoJ,OAAO,EAAC,QAAQ;oBAAAb,QAAA,EAAEpF;kBAAa;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gBAE/CzH,OAAA,CAACf,KAAK;oBAACqK,KAAK;oBAACC,UAAU;oBAAC7B,SAAS,EAAC,MAAM;oBAAAL,QAAA,gBACtCrH,OAAA;sBAAAqH,QAAA,eACErH,OAAA;wBAAAqH,QAAA,gBACErH,OAAA;0BAAAqH,QAAA,EAAI;wBAAQ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACjBzH,OAAA;0BAAAqH,QAAA,EAAI;wBAAI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACbzH,OAAA;0BAAAqH,QAAA,EAAI;wBAAM;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACfzH,OAAA;0BAAAqH,QAAA,EAAI;wBAAM;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACfzH,OAAA;0BAAAqH,QAAA,EAAI;wBAAO;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACRzH,OAAA;sBAAAqH,QAAA,EACGlB,aAAa,CAAChB,MAAM,GAAG,CAAC,GACvBgB,aAAa,CAACqD,GAAG,CAACnD,IAAI,iBACpBrG,OAAA;wBAAAqH,QAAA,gBACErH,OAAA;0BAAAqH,QAAA,EAAKhB,IAAI,CAAChG;wBAAQ;0BAAAiH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxBzH,OAAA;0BAAAqH,QAAA,eACErH,OAAA,CAACd,KAAK;4BAACuK,EAAE,EACPpD,IAAI,CAACG,SAAS,KAAK,OAAO,GAAG,QAAQ,GACrCH,IAAI,CAACG,SAAS,KAAK,mBAAmB,GAAG,SAAS,GAClDH,IAAI,CAACG,SAAS,KAAK,YAAY,GAAG,MAAM,GACxCH,IAAI,CAACG,SAAS,KAAK,mBAAmB,GAAG,SAAS,GAClD,WACD;4BAAAa,QAAA,EACEhB,IAAI,CAACG,SAAS,IAAI;0BAAS;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CAAC,eACLzH,OAAA;0BAAAqH,QAAA,eACErH,OAAA,CAACd,KAAK;4BAACuK,EAAE,EAAEpD,IAAI,CAACqD,SAAS,GAAG,SAAS,GAAG,QAAS;4BAAArC,QAAA,EAC9ChB,IAAI,CAACqD,SAAS,GAAG,QAAQ,GAAG;0BAAU;4BAAApC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CAAC,eACLzH,OAAA;0BAAAqH,QAAA,EAAK,IAAIsC,IAAI,CAACtD,IAAI,CAACuD,WAAW,CAAC,CAACC,kBAAkB,CAAC;wBAAC;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC1DzH,OAAA;0BAAAqH,QAAA,gBACErH,OAAA,CAACnB,MAAM;4BACLqJ,OAAO,EAAC,iBAAiB;4BACzBmB,IAAI,EAAC,IAAI;4BACT3B,SAAS,EAAC,MAAM;4BAChBsB,OAAO,EAAEA,CAAA,KAAMvC,cAAc,CAACJ,IAAI,CAAE;4BAAAgB,QAAA,gBAEpCrH,OAAA,CAACL,MAAM;8BAAC+H,SAAS,EAAC;4BAAM;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,oBAC7B;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACTzH,OAAA,CAACnB,MAAM;4BACLqJ,OAAO,EAAC,gBAAgB;4BACxBmB,IAAI,EAAC,IAAI;4BACTL,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAACZ,IAAI,CAACS,EAAE,CAAE;4BAAAO,QAAA,gBAEzCrH,OAAA,CAACJ,OAAO;8BAAC8H,SAAS,EAAC;4BAAM;8BAAAJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,WAC9B;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC;sBAAA,GAnCEpB,IAAI,CAACS,EAAE;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAoCZ,CACL,CAAC,gBAEFzH,OAAA;wBAAAqH,QAAA,eACErH,OAAA;0BAAI8J,OAAO,EAAC,GAAG;0BAACpC,SAAS,EAAC,aAAa;0BAAAL,QAAA,EACpClF,UAAU,GAAG,4BAA4B,GAAG;wBAAgB;0BAAAmF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBACL;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACR;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACPzH,OAAA;cAAK0H,SAAS,EAAC,kBAAkB;cAAAL,QAAA,eAC/BrH,OAAA;gBAAG0H,SAAS,EAAC,kBAAkB;gBAAAL,QAAA,GAAC,OACvB,EAAC,IAAIsC,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,EAAC,oCACnC;cAAA;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGNzH,OAAA,CAACb,KAAK;MAAC6K,IAAI,EAAEzH,aAAc;MAAC0H,MAAM,EAAEA,CAAA,KAAMzH,gBAAgB,CAAC,KAAK,CAAE;MAAA6E,QAAA,gBAChErH,OAAA,CAACb,KAAK,CAAC+K,MAAM;QAACC,WAAW;QAAA9C,QAAA,eACvBrH,OAAA,CAACb,KAAK,CAACiL,KAAK;UAAA/C,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACfzH,OAAA,CAACb,KAAK,CAAC8I,IAAI;QAAAZ,QAAA,GACRpE,SAAS,iBAAIjD,OAAA,CAAClB,KAAK;UAACoJ,OAAO,EAAC,QAAQ;UAAAb,QAAA,EAAEpE;QAAS;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAEzDzH,OAAA,CAACpB,IAAI;UAAAyI,QAAA,GACF5E,QAAQ,iBACPzC,OAAA;YAAG0H,SAAS,EAAC,MAAM;YAAAL,QAAA,GAAC,8BACU,eAAArH,OAAA;cAAAqH,QAAA,EAAS5E,QAAQ,CAACpC;YAAQ;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CACJ,eAEDzH,OAAA,CAACpB,IAAI,CAACyJ,KAAK;YAACX,SAAS,EAAC,MAAM;YAAAL,QAAA,gBAC1BrH,OAAA,CAACpB,IAAI,CAAC0J,KAAK;cAAAjB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrCzH,OAAA,CAACpB,IAAI,CAAC4J,OAAO;cACXC,IAAI,EAAC,UAAU;cACfvC,KAAK,EAAEvD,YAAa;cACpBgG,QAAQ,EAAGnD,CAAC,IAAK5C,eAAe,CAAC4C,CAAC,CAACS,MAAM,CAACC,KAAK,CAAE;cACjDwC,WAAW,EAAC;YAAoB;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACFzH,OAAA,CAACpB,IAAI,CAAC2J,IAAI;cAACb,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEbzH,OAAA,CAACpB,IAAI,CAACyJ,KAAK;YAACX,SAAS,EAAC,MAAM;YAAAL,QAAA,gBAC1BrH,OAAA,CAACpB,IAAI,CAAC0J,KAAK;cAAAjB,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7CzH,OAAA,CAACpB,IAAI,CAAC4J,OAAO;cACXC,IAAI,EAAC,UAAU;cACfvC,KAAK,EAAErD,mBAAoB;cAC3B8F,QAAQ,EAAGnD,CAAC,IAAK1C,sBAAsB,CAAC0C,CAAC,CAACS,MAAM,CAACC,KAAK,CAAE;cACxDwC,WAAW,EAAC;YAAsB;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbzH,OAAA,CAACb,KAAK,CAACkL,MAAM;QAAAhD,QAAA,gBACXrH,OAAA,CAACnB,MAAM;UAACqJ,OAAO,EAAC,WAAW;UAACc,OAAO,EAAEA,CAAA,KAAMxG,gBAAgB,CAAC,KAAK,CAAE;UAAA6E,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzH,OAAA,CAACnB,MAAM;UACLqJ,OAAO,EAAC,SAAS;UACjBc,OAAO,EAAEpC,cAAe;UACxBsC,QAAQ,EAAEnG,WAAY;UAAAsE,QAAA,EAErBtE,WAAW,gBACV/C,OAAA,CAAAE,SAAA;YAAAmH,QAAA,gBACErH,OAAA,CAAChB,OAAO;cACNmK,EAAE,EAAC,MAAM;cACTC,SAAS,EAAC,QAAQ;cAClBC,IAAI,EAAC,IAAI;cACT1I,IAAI,EAAC,QAAQ;cACb,eAAY,MAAM;cAClB+G,SAAS,EAAC;YAAM;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,aAEJ;UAAA,eAAE,CAAC,GACD;QAAiB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRzH,OAAA,CAACb,KAAK;MAAC6K,IAAI,EAAE3G,eAAgB;MAAC4G,MAAM,EAAEA,CAAA,KAAM3G,kBAAkB,CAAC,KAAK,CAAE;MAAA+D,QAAA,gBACpErH,OAAA,CAACb,KAAK,CAAC+K,MAAM;QAACC,WAAW;QAAA9C,QAAA,eACvBrH,OAAA,CAACb,KAAK,CAACiL,KAAK;UAAA/C,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACfzH,OAAA,CAACb,KAAK,CAAC8I,IAAI;QAAAZ,QAAA,EAAC;MAEZ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzH,OAAA,CAACb,KAAK,CAACkL,MAAM;QAAAhD,QAAA,gBACXrH,OAAA,CAACnB,MAAM;UAACqJ,OAAO,EAAC,WAAW;UAACc,OAAO,EAAEA,CAAA,KAAM1F,kBAAkB,CAAC,KAAK,CAAE;UAAA+D,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzH,OAAA,CAACnB,MAAM;UACLqJ,OAAO,EAAC,QAAQ;UAChBc,OAAO,EAAE7B,mBAAoB;UAC7B+B,QAAQ,EAAEzF,aAAc;UAAA4D,QAAA,EAEvB5D,aAAa,gBACZzD,OAAA,CAAAE,SAAA;YAAAmH,QAAA,gBACErH,OAAA,CAAChB,OAAO;cACNmK,EAAE,EAAC,MAAM;cACTC,SAAS,EAAC,QAAQ;cAClBC,IAAI,EAAC,IAAI;cACT1I,IAAI,EAAC,QAAQ;cACb,eAAY,MAAM;cAClB+G,SAAS,EAAC;YAAM;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eAEJ;UAAA,eAAE,CAAC,GACD;QAAa;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRzH,OAAA;MAAOsK,GAAG,EAAC,MAAM;MAAAjD,QAAA,EAAE;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA,eACV,CAAC;AAEP;AAACrH,EAAA,CAzsBQD,MAAM;AAAAoK,EAAA,GAANpK,MAAM;AA2sBf,eAAeA,MAAM;AAAC,IAAAoK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}