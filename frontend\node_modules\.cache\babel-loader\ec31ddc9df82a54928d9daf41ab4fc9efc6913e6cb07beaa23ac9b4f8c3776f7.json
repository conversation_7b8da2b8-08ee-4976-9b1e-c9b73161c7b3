{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Pri_Fashion_\\\\frontend\\\\src\\\\pages\\\\ViewFabrics.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\nimport { useNavigate } from 'react-router-dom';\nimport { getUserRole, hasRole } from '../utils/auth';\nimport { Container, Row, Col, Card, Form, InputGroup, Button, Badge, Dropdown, DropdownButton, Spinner, Alert, Table, Modal } from 'react-bootstrap';\nimport { FaSearch, FaSort, FaSortUp, FaSortDown, FaEye, FaTshirt, FaCalendarAlt, FaUserTie, FaPalette, FaEdit, FaTrash } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ViewFabrics = () => {\n  _s();\n  const [fabrics, setFabrics] = useState([]);\n  const [filteredFabrics, setFilteredFabrics] = useState([]);\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\n  const [loading, setLoading] = useState(true);\n  const [userRole, setUserRole] = useState(getUserRole());\n  const [isInventoryManager, setIsInventoryManager] = useState(hasRole('Inventory Manager'));\n  const navigate = useNavigate();\n\n  // Delete confirmation modal states\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [fabricToDelete, setFabricToDelete] = useState(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [hasDependencies, setHasDependencies] = useState(false);\n\n  // Search and filter states\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortField, setSortField] = useState('date_added');\n  const [sortOrder, setSortOrder] = useState('desc');\n  const [supplierFilter, setSupplierFilter] = useState('');\n  const [minColorCount, setMinColorCount] = useState('');\n  const [maxColorCount, setMaxColorCount] = useState('');\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [suppliers, setSuppliers] = useState([]);\n\n  // Add resize event listener to update sidebar state\n  useEffect(() => {\n    const handleResize = () => {\n      setIsSidebarOpen(window.innerWidth >= 768);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n\n  // Effect to update user role if it changes in localStorage\n  useEffect(() => {\n    const handleStorageChange = () => {\n      const newRole = getUserRole();\n      if (newRole !== userRole) {\n        setUserRole(newRole);\n        setIsInventoryManager(newRole === 'Inventory Manager');\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, [userRole]);\n\n  // Fetch fabric definitions from your API\n  useEffect(() => {\n    setLoading(true);\n    axios.get(\"http://localhost:8000/api/fabric-definitions/\").then(response => {\n      const fabricData = response.data;\n      setFabrics(fabricData);\n      setFilteredFabrics(fabricData);\n\n      // Extract unique suppliers for filter dropdown\n      const allSuppliers = fabricData.flatMap(fabric => fabric.supplier_names || []);\n      const uniqueSuppliers = [...new Set(allSuppliers)];\n      setSuppliers(uniqueSuppliers);\n      setLoading(false);\n    }).catch(error => {\n      console.error(\"Error fetching fabrics:\", error);\n      setMessage(\"Error loading fabrics.\");\n      setLoading(false);\n    });\n  }, []);\n\n  // Apply filters and sorting whenever dependencies change\n  useEffect(() => {\n    let result = [...fabrics];\n\n    // Apply search filter\n    if (searchTerm) {\n      const term = searchTerm.toLowerCase();\n      result = result.filter(fabric => fabric.fabric_name.toLowerCase().includes(term) || fabric.supplier_names && fabric.supplier_names.some(name => name.toLowerCase().includes(term)));\n    }\n\n    // Apply supplier filter\n    if (supplierFilter) {\n      result = result.filter(fabric => fabric.supplier_names && fabric.supplier_names.includes(supplierFilter));\n    }\n\n    // Apply color count range filter\n    if (minColorCount !== '') {\n      result = result.filter(fabric => fabric.variant_count >= parseInt(minColorCount));\n    }\n    if (maxColorCount !== '') {\n      result = result.filter(fabric => fabric.variant_count <= parseInt(maxColorCount));\n    }\n\n    // Apply date range filter\n    if (startDate) {\n      result = result.filter(fabric => new Date(fabric.date_added) >= new Date(startDate));\n    }\n    if (endDate) {\n      result = result.filter(fabric => new Date(fabric.date_added) <= new Date(endDate));\n    }\n\n    // Apply sorting\n    result.sort((a, b) => {\n      let aVal = a[sortField];\n      let bVal = b[sortField];\n\n      // Handle date sorting\n      if (sortField === 'date_added') {\n        aVal = new Date(aVal);\n        bVal = new Date(bVal);\n      }\n      // Handle numeric sorting\n      else if (sortField === 'variant_count' || sortField === 'id') {\n        aVal = parseInt(aVal);\n        bVal = parseInt(bVal);\n      }\n      // Handle string sorting\n      else {\n        aVal = aVal.toString().toLowerCase();\n        bVal = bVal.toString().toLowerCase();\n      }\n      if (aVal < bVal) return sortOrder === 'asc' ? -1 : 1;\n      if (aVal > bVal) return sortOrder === 'asc' ? 1 : -1;\n      return 0;\n    });\n    setFilteredFabrics(result);\n  }, [fabrics, searchTerm, sortField, sortOrder, supplierFilter, minColorCount, maxColorCount, startDate, endDate]);\n\n  // Handler to navigate to the fabric variants page\n  const handleViewVariants = fabricId => {\n    navigate(`/fabric-definitions/${fabricId}`);\n  };\n\n  // Handler for sorting\n  const handleSort = field => {\n    if (sortField === field) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortOrder('asc');\n    }\n  };\n\n  // Get sort icon based on current sort state\n  const getSortIcon = field => {\n    if (sortField !== field) return /*#__PURE__*/_jsxDEV(FaSort, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 37\n    }, this);\n    return sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(FaSortUp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 34\n    }, this) : /*#__PURE__*/_jsxDEV(FaSortDown, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 49\n    }, this);\n  };\n\n  // Reset all filters\n  const resetFilters = () => {\n    setSearchTerm('');\n    setSupplierFilter('');\n    setMinColorCount('');\n    setMaxColorCount('');\n    setStartDate('');\n    setEndDate('');\n    setSortField('date_added');\n    setSortOrder('desc');\n  };\n\n  // Format date for display\n  const formatDate = dateString => {\n    const options = {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    };\n    return new Date(dateString).toLocaleDateString(undefined, options);\n  };\n\n  // Handle edit fabric\n  const handleEditFabric = fabricId => {\n    navigate(`/edit-fabric/${fabricId}`);\n  };\n\n  // Check if fabric has any cutting records associated with it\n  const checkFabricDependencies = async fabricId => {\n    try {\n      console.log(`Checking dependencies for fabric definition ${fabricId}`);\n\n      // Direct check for cutting records that use this fabric definition\n      const cuttingResponse = await axios.get(`http://localhost:8000/api/cutting/cutting-records/`);\n      console.log(\"All cutting records:\", cuttingResponse.data);\n\n      // Check if any cutting record directly references this fabric definition\n      const hasCuttingRecords = cuttingResponse.data.some(record => record.fabric_definition === fabricId || record.fabric_definition === parseInt(fabricId));\n      if (hasCuttingRecords) {\n        console.log(`Fabric definition ${fabricId} is directly used in cutting records`);\n        return true;\n      }\n\n      // Check for cutting records at the variant level\n      try {\n        // First, get all variants for this fabric definition\n        const variantsResponse = await axios.get(`http://localhost:8000/api/fabric-definitions/${fabricId}/variants/`);\n        const variants = variantsResponse.data;\n        console.log(`Found ${variants.length} variants for fabric definition ${fabricId}:`, variants);\n\n        // Check if any cutting record details reference any of these variants\n        for (const record of cuttingResponse.data) {\n          if (record.details && record.details.length > 0) {\n            for (const variant of variants) {\n              // Check if any detail uses this variant\n              const usesVariant = record.details.some(detail => detail.fabric_variant === variant.id || detail.fabric_variant === parseInt(variant.id));\n              if (usesVariant) {\n                console.log(`Variant ${variant.id} is used in cutting record ${record.id}`);\n                return true;\n              }\n            }\n          }\n        }\n\n        // Fallback to checking each variant's history if needed\n        for (const variant of variants) {\n          try {\n            console.log(`Checking cutting history for variant ${variant.id}`);\n            const variantHistoryResponse = await axios.get(`http://localhost:8000/api/cutting/fabric-variant/${variant.id}/history/`);\n            console.log(`Variant ${variant.id} history:`, variantHistoryResponse.data);\n            if (variantHistoryResponse.data && variantHistoryResponse.data.cutting_history && variantHistoryResponse.data.cutting_history.length > 0) {\n              console.log(`Variant ${variant.id} has ${variantHistoryResponse.data.cutting_history.length} cutting records`);\n              return true; // Found cutting records for this variant\n            }\n          } catch (error) {\n            console.error(`Error checking cutting history for variant ${variant.id}:`, error);\n\n            // If it's a 404 error, it means the endpoint doesn't exist or the variant doesn't exist\n            // In this case, we can continue checking other variants\n            if (error.response && error.response.status === 404) {\n              console.log(`Variant ${variant.id} not found or endpoint not available`);\n              continue;\n            }\n\n            // For other errors, log more details but continue checking other variants\n            console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\n          }\n        }\n      } catch (error) {\n        console.error(`Error fetching variants for fabric definition ${fabricId}:`, error);\n        console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\n\n        // If we can't check variants, assume there might be dependencies\n        return true;\n      }\n\n      // No cutting records found - fabric can be safely deleted\n      console.log(`No cutting records found for fabric definition ${fabricId} or its variants`);\n      return false;\n    } catch (error) {\n      console.error(\"Error checking fabric dependencies:\", error);\n      console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\n\n      // If there's an error, assume there might be dependencies to prevent accidental deletion\n      return true;\n    }\n  };\n\n  // Handle delete button click\n  const handleDeleteClick = async fabric => {\n    setFabricToDelete(fabric);\n    setIsDeleting(false);\n\n    // Check for dependencies\n    const hasDeps = await checkFabricDependencies(fabric.id);\n    setHasDependencies(hasDeps);\n\n    // Show the confirmation modal\n    setShowDeleteModal(true);\n  };\n\n  // Handle delete confirmation\n  const confirmDelete = async () => {\n    if (hasDependencies) {\n      return; // Don't allow deletion if there are dependencies\n    }\n    setIsDeleting(true);\n    try {\n      console.log(`Attempting to delete fabric definition with ID: ${fabricToDelete.id}`);\n\n      // Try to delete all variants first if there are any issues\n      try {\n        // Get all variants for this fabric definition\n        const variantsResponse = await axios.get(`http://localhost:8000/api/fabric-definitions/${fabricToDelete.id}/variants/`);\n        const variants = variantsResponse.data;\n        console.log(`Found ${variants.length} variants to delete for fabric definition ${fabricToDelete.id}`);\n\n        // Try to delete each variant individually\n        for (const variant of variants) {\n          try {\n            console.log(`Attempting to delete variant ${variant.id}`);\n\n            // First try to update the variant to have 0 yards\n            await axios.put(`http://localhost:8000/api/fabric-variants/${variant.id}/`, {\n              fabric_definition: fabricToDelete.id,\n              color: variant.color,\n              color_name: variant.color_name || variant.color,\n              total_yard: 0,\n              available_yard: 0,\n              price_per_yard: 0\n            });\n\n            // Then try to delete it\n            await axios.delete(`http://localhost:8000/api/fabric-variants/${variant.id}/`);\n            console.log(`Successfully deleted variant ${variant.id}`);\n          } catch (variantError) {\n            console.error(`Error deleting variant ${variant.id}:`, variantError);\n            // Continue with other variants even if one fails\n          }\n        }\n      } catch (variantsError) {\n        console.error(\"Error handling variants:\", variantsError);\n        // Continue with fabric definition deletion even if variants handling fails\n      }\n\n      // Now try to delete the fabric definition\n      const response = await axios.delete(`http://localhost:8000/api/fabric-definitions/${fabricToDelete.id}/`);\n      console.log(\"Delete response:\", response);\n\n      // Remove the deleted fabric from the lists\n      const updatedFabrics = fabrics.filter(f => f.id !== fabricToDelete.id);\n      setFabrics(updatedFabrics);\n      setFilteredFabrics(filteredFabrics.filter(f => f.id !== fabricToDelete.id));\n\n      // Close the modal and show success message\n      setShowDeleteModal(false);\n      setSuccess(`Fabric \"${fabricToDelete.fabric_name}\" and all its variants deleted successfully!`);\n\n      // Clear success message after 3 seconds\n      setTimeout(() => {\n        setSuccess(\"\");\n      }, 3000);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Error deleting fabric:\", error);\n\n      // Log detailed error information\n      console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\n      console.error(\"Error status:\", error.response ? error.response.status : \"No status\");\n\n      // Provide more specific error messages based on the error\n      let errorMessage = \"Failed to delete fabric. Please try again.\";\n      if (error.response) {\n        if (error.response.status === 400) {\n          errorMessage = error.response.data.detail || \"Bad request. The fabric may be referenced by other records.\";\n        } else if (error.response.status === 403) {\n          errorMessage = \"You don't have permission to delete this fabric.\";\n        } else if (error.response.status === 404) {\n          errorMessage = \"Fabric not found. It may have been already deleted.\";\n        } else if (error.response.status === 500) {\n          errorMessage = \"Server error. Please contact the administrator.\";\n        }\n      }\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || errorMessage);\n\n      // Clear error after 5 seconds\n      setTimeout(() => {\n        setError(\"\");\n      }, 5000);\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  // Close the delete modal\n  const closeDeleteModal = () => {\n    setShowDeleteModal(false);\n    setFabricToDelete(null);\n    setHasDependencies(false);\n  };\n\n  // Render enhanced table view\n  const renderTableView = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-responsive\",\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        hover: true,\n        bordered: true,\n        className: \"align-middle shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-light\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              onClick: () => handleSort('id'),\n              style: {\n                cursor: 'pointer',\n                width: '5%'\n              },\n              className: \"text-center\",\n              children: [\"ID \", getSortIcon('id')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              onClick: () => handleSort('fabric_name'),\n              style: {\n                cursor: 'pointer',\n                width: '25%'\n              },\n              children: [\"Fabric Name \", getSortIcon('fabric_name')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              onClick: () => handleSort('supplier_name'),\n              style: {\n                cursor: 'pointer',\n                width: '20%'\n              },\n              children: [\"Supplier \", getSortIcon('supplier_name')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              onClick: () => handleSort('date_added'),\n              style: {\n                cursor: 'pointer',\n                width: '15%'\n              },\n              children: [\"Date Added \", getSortIcon('date_added')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              onClick: () => handleSort('variant_count'),\n              style: {\n                cursor: 'pointer',\n                width: '15%'\n              },\n              className: \"text-center\",\n              children: [\"Colors \", getSortIcon('variant_count')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              style: {\n                width: '25%'\n              },\n              className: \"text-center\",\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: filteredFabrics.map(fabric => /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: \"align-middle\",\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"text-center\",\n              children: fabric.id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaTshirt, {\n                  className: \"me-2 text-primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    cursor: 'pointer',\n                    fontWeight: 'bold'\n                  },\n                  onClick: () => handleViewVariants(fabric.id),\n                  children: fabric.fabric_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaUserTie, {\n                  className: \"me-2 text-secondary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 21\n                }, this), fabric.supplier_name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                  className: \"me-2 text-secondary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 21\n                }, this), formatDate(fabric.date_added)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                bg: \"primary\",\n                pill: true,\n                style: {\n                  fontSize: '0.9rem',\n                  padding: '0.4rem 0.8rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaPalette, {\n                  className: \"me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this), \" \", fabric.variant_count]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"info\",\n                  className: \"btn-sm\",\n                  onClick: () => handleViewVariants(fabric.id),\n                  children: [/*#__PURE__*/_jsxDEV(FaEye, {\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 23\n                  }, this), \" View\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 21\n                }, this), isInventoryManager && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"warning\",\n                    className: \"btn-sm\",\n                    onClick: () => handleEditFabric(fabric.id),\n                    children: [/*#__PURE__*/_jsxDEV(FaEdit, {\n                      className: \"me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 27\n                    }, this), \" Edit\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"danger\",\n                    className: \"btn-sm\",\n                    onClick: () => handleDeleteClick(fabric),\n                    children: [/*#__PURE__*/_jsxDEV(FaTrash, {\n                      className: \"me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 499,\n                      columnNumber: 27\n                    }, this), \" Delete\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 17\n            }, this)]\n          }, fabric.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      style: {\n        marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\n        width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\n        transition: \"all 0.3s ease\",\n        padding: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(FaTshirt, {\n            className: \"me-2 text-primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this), \"Fabric Inventory\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this), isInventoryManager && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"success\",\n          onClick: () => navigate('/addfabric'),\n          children: \"Add New Fabric\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"success\",\n        className: \"text-center\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 21\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"text-center\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 19\n      }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"success\",\n        className: \"text-center\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        show: showDeleteModal,\n        onHide: closeDeleteModal,\n        centered: true,\n        children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n          closeButton: true,\n          children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n            children: \"Confirm Deletion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: fabricToDelete && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Are you sure you want to delete this fabric?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Table, {\n              bordered: true,\n              size: \"sm\",\n              className: \"mt-3\",\n              children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Fabric Name:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 555,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: fabricToDelete.fabric_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Supplier:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 559,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: fabricToDelete.supplier_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Date Added:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatDate(fabricToDelete.date_added)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Color Variants:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: fabricToDelete.variant_count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 17\n            }, this), hasDependencies && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"warning\",\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Warning:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 21\n              }, this), \" This fabric cannot be deleted because it or one of its color variants is being used in cutting records.\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"To delete this fabric, you must first delete all cutting records that use it or any of its color variants. You can view these records by clicking \\\"View\\\" to see the variants, then \\\"View Inventory\\\" for each variant to check its cutting history.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 19\n            }, this), !hasDependencies && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"success\",\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Good news!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 21\n              }, this), \" This fabric and all its variants have no cutting records and can be safely deleted.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: closeDeleteModal,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"danger\",\n            onClick: confirmDelete,\n            disabled: isDeleting || hasDependencies,\n            children: isDeleting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                as: \"span\",\n                animation: \"border\",\n                size: \"sm\",\n                role: \"status\",\n                \"aria-hidden\": \"true\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 19\n              }, this), \"Deleting...\"]\n            }, void 0, true) : 'Delete Fabric'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"mb-4 shadow-sm\",\n        style: {\n          backgroundColor: '#f8f9fa'\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            className: \"g-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              lg: 4,\n              md: 6,\n              sm: 12,\n              children: /*#__PURE__*/_jsxDEV(InputGroup, {\n                children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                  className: \"bg-white\",\n                  children: /*#__PURE__*/_jsxDEV(FaSearch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  placeholder: \"Search by fabric name or supplier...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  className: \"border-start-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 19\n                }, this), searchTerm && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-secondary\",\n                  onClick: () => setSearchTerm(''),\n                  children: \"Clear\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              lg: 3,\n              md: 6,\n              sm: 12,\n              children: /*#__PURE__*/_jsxDEV(DropdownButton, {\n                id: \"supplier-filter\",\n                title: supplierFilter || \"Filter by Supplier\",\n                variant: \"outline-primary\",\n                className: \"w-100\",\n                children: [/*#__PURE__*/_jsxDEV(Dropdown.Item, {\n                  onClick: () => setSupplierFilter(''),\n                  children: \"All Suppliers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Dropdown.Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 19\n                }, this), suppliers.map((supplier, index) => /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n                  onClick: () => setSupplierFilter(supplier),\n                  active: supplierFilter === supplier,\n                  children: supplier\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              lg: 3,\n              md: 6,\n              sm: 12,\n              children: /*#__PURE__*/_jsxDEV(InputGroup, {\n                children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                  className: \"bg-white\",\n                  children: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  placeholder: \"From\",\n                  value: startDate,\n                  onChange: e => setStartDate(e.target.value),\n                  className: \"border-start-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  placeholder: \"To\",\n                  value: endDate,\n                  onChange: e => setEndDate(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              lg: 2,\n              md: 6,\n              sm: 12,\n              children: /*#__PURE__*/_jsxDEV(InputGroup, {\n                children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                  className: \"bg-white\",\n                  children: /*#__PURE__*/_jsxDEV(FaPalette, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  placeholder: \"Min\",\n                  min: \"0\",\n                  value: minColorCount,\n                  onChange: e => setMinColorCount(e.target.value),\n                  className: \"border-start-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  placeholder: \"Max\",\n                  min: \"0\",\n                  value: maxColorCount,\n                  onChange: e => setMaxColorCount(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-between mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Badge, {\n                bg: \"primary\",\n                className: \"me-2 p-2\",\n                children: [filteredFabrics.length, \" fabrics found\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 17\n              }, this), (searchTerm || supplierFilter || minColorCount || maxColorCount || startDate || endDate) && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-danger\",\n                size: \"sm\",\n                onClick: resetFilters,\n                children: \"Reset Filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Form.Select, {\n                size: \"sm\",\n                style: {\n                  width: 'auto',\n                  display: 'inline-block'\n                },\n                value: `${sortField}-${sortOrder}`,\n                onChange: e => {\n                  const [field, order] = e.target.value.split('-');\n                  setSortField(field);\n                  setSortOrder(order);\n                },\n                className: \"border-primary\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"date_added-desc\",\n                  children: \"Newest First\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"date_added-asc\",\n                  children: \"Oldest First\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"fabric_name-asc\",\n                  children: \"Name (A-Z)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"fabric_name-desc\",\n                  children: \"Name (Z-A)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"supplier_name-asc\",\n                  children: \"Supplier (A-Z)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"supplier_name-desc\",\n                  children: \"Supplier (Z-A)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"variant_count-desc\",\n                  children: \"Most Colors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 742,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"variant_count-asc\",\n                  children: \"Fewest Colors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center my-5\",\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          role: \"status\",\n          variant: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2\",\n          children: \"Loading fabrics...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 752,\n        columnNumber: 11\n      }, this) : filteredFabrics.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"info\",\n        className: \"text-center\",\n        children: \"No fabrics found matching your filters. Try adjusting your search criteria.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 759,\n        columnNumber: 11\n      }, this) : renderTableView()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ViewFabrics, \"9n3TRP4QZC1yVoOdjLdBiN+nZqQ=\", false, function () {\n  return [useNavigate];\n});\n_c = ViewFabrics;\nexport default ViewFabrics;\nvar _c;\n$RefreshReg$(_c, \"ViewFabrics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "RoleBasedNavBar", "useNavigate", "getUserRole", "hasRole", "Container", "Row", "Col", "Card", "Form", "InputGroup", "<PERSON><PERSON>", "Badge", "Dropdown", "DropdownButton", "Spinner", "<PERSON><PERSON>", "Table", "Modal", "FaSearch", "FaSort", "FaSortUp", "FaSortDown", "FaEye", "FaTshirt", "FaCalendarAlt", "FaUserTie", "FaPalette", "FaEdit", "FaTrash", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ViewFabrics", "_s", "fabrics", "setFabrics", "filteredFabrics", "setFilteredFabrics", "message", "setMessage", "error", "setError", "success", "setSuccess", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "loading", "setLoading", "userRole", "setUserRole", "isInventoryManager", "setIsInventoryManager", "navigate", "showDeleteModal", "setShowDeleteModal", "fabricToDelete", "setFabricToDelete", "isDeleting", "setIsDeleting", "hasDependencies", "setHasDependencies", "searchTerm", "setSearchTerm", "sortField", "setSortField", "sortOrder", "setSortOrder", "supplierFilter", "setSupplier<PERSON><PERSON>er", "minColorCount", "setMinColorCount", "maxColorCount", "setMaxColorCount", "startDate", "setStartDate", "endDate", "setEndDate", "suppliers", "setSuppliers", "handleResize", "addEventListener", "removeEventListener", "handleStorageChange", "newRole", "get", "then", "response", "fabricData", "data", "allSuppliers", "flatMap", "fabric", "supplier_names", "uniqueSuppliers", "Set", "catch", "console", "result", "term", "toLowerCase", "filter", "fabric_name", "includes", "some", "name", "variant_count", "parseInt", "Date", "date_added", "sort", "a", "b", "aVal", "bVal", "toString", "handleViewVariants", "fabricId", "handleSort", "field", "getSortIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "resetFilters", "formatDate", "dateString", "options", "year", "month", "day", "toLocaleDateString", "undefined", "handleEditFabric", "checkFabricDependencies", "log", "cuttingResponse", "hasCuttingRecords", "record", "fabric_definition", "variantsResponse", "variants", "length", "details", "variant", "usesVariant", "detail", "fabric_variant", "id", "variantHistoryResponse", "cutting_history", "status", "handleDeleteClick", "hasDeps", "confirmDelete", "put", "color", "color_name", "total_yard", "available_yard", "price_per_yard", "delete", "variantError", "variantsError", "updatedFabrics", "f", "setTimeout", "_error$response", "_error$response$data", "errorMessage", "closeDeleteModal", "renderTableView", "className", "children", "hover", "bordered", "onClick", "style", "cursor", "width", "map", "fontWeight", "supplier_name", "bg", "pill", "fontSize", "padding", "fluid", "marginLeft", "transition", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "size", "Footer", "disabled", "as", "animation", "role", "backgroundColor", "lg", "md", "sm", "Text", "Control", "placeholder", "value", "onChange", "e", "target", "title", "<PERSON><PERSON>", "Divider", "supplier", "index", "active", "type", "min", "Select", "display", "order", "split", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Pri_Fashion_/frontend/src/pages/ViewFabrics.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { getUserRole, hasRole } from '../utils/auth';\r\nimport {\r\n  Container, Row, Col, Card, Form, InputGroup,\r\n  Button, Badge, Dropdown, DropdownButton,\r\n  Spinner, Alert, Table, Modal\r\n} from 'react-bootstrap';\r\nimport {\r\n  FaSearch, FaSort, FaSortUp, FaSortDown,\r\n  FaEye, FaTshirt, FaCalendarAlt,\r\n  FaUserTie, FaPalette, FaEdit, FaTrash\r\n} from 'react-icons/fa';\r\n\r\nconst ViewFabrics = () => {\r\n  const [fabrics, setFabrics] = useState([]);\r\n  const [filteredFabrics, setFilteredFabrics] = useState([]);\r\n  const [message, setMessage] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [loading, setLoading] = useState(true);\r\n  const [userRole, setUserRole] = useState(getUserRole());\r\n  const [isInventoryManager, setIsInventoryManager] = useState(hasRole('Inventory Manager'));\r\n  const navigate = useNavigate();\r\n\r\n  // Delete confirmation modal states\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [fabricToDelete, setFabricToDelete] = useState(null);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [hasDependencies, setHasDependencies] = useState(false);\r\n\r\n  // Search and filter states\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [sortField, setSortField] = useState('date_added');\r\n  const [sortOrder, setSortOrder] = useState('desc');\r\n  const [supplierFilter, setSupplierFilter] = useState('');\r\n  const [minColorCount, setMinColorCount] = useState('');\r\n  const [maxColorCount, setMaxColorCount] = useState('');\r\n  const [startDate, setStartDate] = useState('');\r\n  const [endDate, setEndDate] = useState('');\r\n  const [suppliers, setSuppliers] = useState([]);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Effect to update user role if it changes in localStorage\r\n  useEffect(() => {\r\n    const handleStorageChange = () => {\r\n      const newRole = getUserRole();\r\n      if (newRole !== userRole) {\r\n        setUserRole(newRole);\r\n        setIsInventoryManager(newRole === 'Inventory Manager');\r\n      }\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, [userRole]);\r\n\r\n  // Fetch fabric definitions from your API\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    axios\r\n      .get(\"http://localhost:8000/api/fabric-definitions/\")\r\n      .then((response) => {\r\n        const fabricData = response.data;\r\n        setFabrics(fabricData);\r\n        setFilteredFabrics(fabricData);\r\n\r\n        // Extract unique suppliers for filter dropdown\r\n        const allSuppliers = fabricData.flatMap(fabric => fabric.supplier_names || []);\r\n        const uniqueSuppliers = [...new Set(allSuppliers)];\r\n        setSuppliers(uniqueSuppliers);\r\n\r\n        setLoading(false);\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"Error fetching fabrics:\", error);\r\n        setMessage(\"Error loading fabrics.\");\r\n        setLoading(false);\r\n      });\r\n  }, []);\r\n\r\n  // Apply filters and sorting whenever dependencies change\r\n  useEffect(() => {\r\n    let result = [...fabrics];\r\n\r\n    // Apply search filter\r\n    if (searchTerm) {\r\n      const term = searchTerm.toLowerCase();\r\n      result = result.filter(\r\n        fabric =>\r\n          fabric.fabric_name.toLowerCase().includes(term) ||\r\n          (fabric.supplier_names && fabric.supplier_names.some(name =>\r\n            name.toLowerCase().includes(term)\r\n          ))\r\n      );\r\n    }\r\n\r\n    // Apply supplier filter\r\n    if (supplierFilter) {\r\n      result = result.filter(fabric =>\r\n        fabric.supplier_names && fabric.supplier_names.includes(supplierFilter)\r\n      );\r\n    }\r\n\r\n    // Apply color count range filter\r\n    if (minColorCount !== '') {\r\n      result = result.filter(fabric => fabric.variant_count >= parseInt(minColorCount));\r\n    }\r\n    if (maxColorCount !== '') {\r\n      result = result.filter(fabric => fabric.variant_count <= parseInt(maxColorCount));\r\n    }\r\n\r\n    // Apply date range filter\r\n    if (startDate) {\r\n      result = result.filter(fabric => new Date(fabric.date_added) >= new Date(startDate));\r\n    }\r\n    if (endDate) {\r\n      result = result.filter(fabric => new Date(fabric.date_added) <= new Date(endDate));\r\n    }\r\n\r\n    // Apply sorting\r\n    result.sort((a, b) => {\r\n      let aVal = a[sortField];\r\n      let bVal = b[sortField];\r\n\r\n      // Handle date sorting\r\n      if (sortField === 'date_added') {\r\n        aVal = new Date(aVal);\r\n        bVal = new Date(bVal);\r\n      }\r\n      // Handle numeric sorting\r\n      else if (sortField === 'variant_count' || sortField === 'id') {\r\n        aVal = parseInt(aVal);\r\n        bVal = parseInt(bVal);\r\n      }\r\n      // Handle string sorting\r\n      else {\r\n        aVal = aVal.toString().toLowerCase();\r\n        bVal = bVal.toString().toLowerCase();\r\n      }\r\n\r\n      if (aVal < bVal) return sortOrder === 'asc' ? -1 : 1;\r\n      if (aVal > bVal) return sortOrder === 'asc' ? 1 : -1;\r\n      return 0;\r\n    });\r\n\r\n    setFilteredFabrics(result);\r\n  }, [fabrics, searchTerm, sortField, sortOrder, supplierFilter, minColorCount, maxColorCount, startDate, endDate]);\r\n\r\n  // Handler to navigate to the fabric variants page\r\n  const handleViewVariants = (fabricId) => {\r\n    navigate(`/fabric-definitions/${fabricId}`);\r\n  };\r\n\r\n  // Handler for sorting\r\n  const handleSort = (field) => {\r\n    if (sortField === field) {\r\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\r\n    } else {\r\n      setSortField(field);\r\n      setSortOrder('asc');\r\n    }\r\n  };\r\n\r\n  // Get sort icon based on current sort state\r\n  const getSortIcon = (field) => {\r\n    if (sortField !== field) return <FaSort />;\r\n    return sortOrder === 'asc' ? <FaSortUp /> : <FaSortDown />;\r\n  };\r\n\r\n  // Reset all filters\r\n  const resetFilters = () => {\r\n    setSearchTerm('');\r\n    setSupplierFilter('');\r\n    setMinColorCount('');\r\n    setMaxColorCount('');\r\n    setStartDate('');\r\n    setEndDate('');\r\n    setSortField('date_added');\r\n    setSortOrder('desc');\r\n  };\r\n\r\n  // Format date for display\r\n  const formatDate = (dateString) => {\r\n    const options = { year: 'numeric', month: 'short', day: 'numeric' };\r\n    return new Date(dateString).toLocaleDateString(undefined, options);\r\n  };\r\n\r\n  // Handle edit fabric\r\n  const handleEditFabric = (fabricId) => {\r\n    navigate(`/edit-fabric/${fabricId}`);\r\n  };\r\n\r\n  // Check if fabric has any cutting records associated with it\r\n  const checkFabricDependencies = async (fabricId) => {\r\n    try {\r\n      console.log(`Checking dependencies for fabric definition ${fabricId}`);\r\n\r\n      // Direct check for cutting records that use this fabric definition\r\n      const cuttingResponse = await axios.get(`http://localhost:8000/api/cutting/cutting-records/`);\r\n      console.log(\"All cutting records:\", cuttingResponse.data);\r\n\r\n      // Check if any cutting record directly references this fabric definition\r\n      const hasCuttingRecords = cuttingResponse.data.some(\r\n        record => record.fabric_definition === fabricId || record.fabric_definition === parseInt(fabricId)\r\n      );\r\n\r\n      if (hasCuttingRecords) {\r\n        console.log(`Fabric definition ${fabricId} is directly used in cutting records`);\r\n        return true;\r\n      }\r\n\r\n      // Check for cutting records at the variant level\r\n      try {\r\n        // First, get all variants for this fabric definition\r\n        const variantsResponse = await axios.get(`http://localhost:8000/api/fabric-definitions/${fabricId}/variants/`);\r\n        const variants = variantsResponse.data;\r\n        console.log(`Found ${variants.length} variants for fabric definition ${fabricId}:`, variants);\r\n\r\n        // Check if any cutting record details reference any of these variants\r\n        for (const record of cuttingResponse.data) {\r\n          if (record.details && record.details.length > 0) {\r\n            for (const variant of variants) {\r\n              // Check if any detail uses this variant\r\n              const usesVariant = record.details.some(\r\n                detail => detail.fabric_variant === variant.id || detail.fabric_variant === parseInt(variant.id)\r\n              );\r\n\r\n              if (usesVariant) {\r\n                console.log(`Variant ${variant.id} is used in cutting record ${record.id}`);\r\n                return true;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // Fallback to checking each variant's history if needed\r\n        for (const variant of variants) {\r\n          try {\r\n            console.log(`Checking cutting history for variant ${variant.id}`);\r\n            const variantHistoryResponse = await axios.get(`http://localhost:8000/api/cutting/fabric-variant/${variant.id}/history/`);\r\n            console.log(`Variant ${variant.id} history:`, variantHistoryResponse.data);\r\n\r\n            if (variantHistoryResponse.data &&\r\n                variantHistoryResponse.data.cutting_history &&\r\n                variantHistoryResponse.data.cutting_history.length > 0) {\r\n              console.log(`Variant ${variant.id} has ${variantHistoryResponse.data.cutting_history.length} cutting records`);\r\n              return true; // Found cutting records for this variant\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error checking cutting history for variant ${variant.id}:`, error);\r\n\r\n            // If it's a 404 error, it means the endpoint doesn't exist or the variant doesn't exist\r\n            // In this case, we can continue checking other variants\r\n            if (error.response && error.response.status === 404) {\r\n              console.log(`Variant ${variant.id} not found or endpoint not available`);\r\n              continue;\r\n            }\r\n\r\n            // For other errors, log more details but continue checking other variants\r\n            console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error fetching variants for fabric definition ${fabricId}:`, error);\r\n        console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\r\n\r\n        // If we can't check variants, assume there might be dependencies\r\n        return true;\r\n      }\r\n\r\n      // No cutting records found - fabric can be safely deleted\r\n      console.log(`No cutting records found for fabric definition ${fabricId} or its variants`);\r\n      return false;\r\n    } catch (error) {\r\n      console.error(\"Error checking fabric dependencies:\", error);\r\n      console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\r\n\r\n      // If there's an error, assume there might be dependencies to prevent accidental deletion\r\n      return true;\r\n    }\r\n  };\r\n\r\n  // Handle delete button click\r\n  const handleDeleteClick = async (fabric) => {\r\n    setFabricToDelete(fabric);\r\n    setIsDeleting(false);\r\n\r\n    // Check for dependencies\r\n    const hasDeps = await checkFabricDependencies(fabric.id);\r\n    setHasDependencies(hasDeps);\r\n\r\n    // Show the confirmation modal\r\n    setShowDeleteModal(true);\r\n  };\r\n\r\n  // Handle delete confirmation\r\n  const confirmDelete = async () => {\r\n    if (hasDependencies) {\r\n      return; // Don't allow deletion if there are dependencies\r\n    }\r\n\r\n    setIsDeleting(true);\r\n\r\n    try {\r\n      console.log(`Attempting to delete fabric definition with ID: ${fabricToDelete.id}`);\r\n\r\n      // Try to delete all variants first if there are any issues\r\n      try {\r\n        // Get all variants for this fabric definition\r\n        const variantsResponse = await axios.get(`http://localhost:8000/api/fabric-definitions/${fabricToDelete.id}/variants/`);\r\n        const variants = variantsResponse.data;\r\n        console.log(`Found ${variants.length} variants to delete for fabric definition ${fabricToDelete.id}`);\r\n\r\n        // Try to delete each variant individually\r\n        for (const variant of variants) {\r\n          try {\r\n            console.log(`Attempting to delete variant ${variant.id}`);\r\n\r\n            // First try to update the variant to have 0 yards\r\n            await axios.put(`http://localhost:8000/api/fabric-variants/${variant.id}/`, {\r\n              fabric_definition: fabricToDelete.id,\r\n              color: variant.color,\r\n              color_name: variant.color_name || variant.color,\r\n              total_yard: 0,\r\n              available_yard: 0,\r\n              price_per_yard: 0\r\n            });\r\n\r\n            // Then try to delete it\r\n            await axios.delete(`http://localhost:8000/api/fabric-variants/${variant.id}/`);\r\n            console.log(`Successfully deleted variant ${variant.id}`);\r\n          } catch (variantError) {\r\n            console.error(`Error deleting variant ${variant.id}:`, variantError);\r\n            // Continue with other variants even if one fails\r\n          }\r\n        }\r\n      } catch (variantsError) {\r\n        console.error(\"Error handling variants:\", variantsError);\r\n        // Continue with fabric definition deletion even if variants handling fails\r\n      }\r\n\r\n      // Now try to delete the fabric definition\r\n      const response = await axios.delete(`http://localhost:8000/api/fabric-definitions/${fabricToDelete.id}/`);\r\n      console.log(\"Delete response:\", response);\r\n\r\n      // Remove the deleted fabric from the lists\r\n      const updatedFabrics = fabrics.filter(f => f.id !== fabricToDelete.id);\r\n      setFabrics(updatedFabrics);\r\n      setFilteredFabrics(filteredFabrics.filter(f => f.id !== fabricToDelete.id));\r\n\r\n      // Close the modal and show success message\r\n      setShowDeleteModal(false);\r\n      setSuccess(`Fabric \"${fabricToDelete.fabric_name}\" and all its variants deleted successfully!`);\r\n\r\n      // Clear success message after 3 seconds\r\n      setTimeout(() => {\r\n        setSuccess(\"\");\r\n      }, 3000);\r\n    } catch (error) {\r\n      console.error(\"Error deleting fabric:\", error);\r\n\r\n      // Log detailed error information\r\n      console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\r\n      console.error(\"Error status:\", error.response ? error.response.status : \"No status\");\r\n\r\n      // Provide more specific error messages based on the error\r\n      let errorMessage = \"Failed to delete fabric. Please try again.\";\r\n\r\n      if (error.response) {\r\n        if (error.response.status === 400) {\r\n          errorMessage = error.response.data.detail || \"Bad request. The fabric may be referenced by other records.\";\r\n        } else if (error.response.status === 403) {\r\n          errorMessage = \"You don't have permission to delete this fabric.\";\r\n        } else if (error.response.status === 404) {\r\n          errorMessage = \"Fabric not found. It may have been already deleted.\";\r\n        } else if (error.response.status === 500) {\r\n          errorMessage = \"Server error. Please contact the administrator.\";\r\n        }\r\n      }\r\n\r\n      setError(error.response?.data?.message || errorMessage);\r\n\r\n      // Clear error after 5 seconds\r\n      setTimeout(() => {\r\n        setError(\"\");\r\n      }, 5000);\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  // Close the delete modal\r\n  const closeDeleteModal = () => {\r\n    setShowDeleteModal(false);\r\n    setFabricToDelete(null);\r\n    setHasDependencies(false);\r\n  };\r\n\r\n  // Render enhanced table view\r\n  const renderTableView = () => {\r\n    return (\r\n      <div className=\"table-responsive\">\r\n        <Table hover bordered className=\"align-middle shadow-sm\">\r\n          <thead className=\"bg-light\">\r\n            <tr>\r\n              <th onClick={() => handleSort('id')} style={{ cursor: 'pointer', width: '5%' }} className=\"text-center\">\r\n                ID {getSortIcon('id')}\r\n              </th>\r\n              <th onClick={() => handleSort('fabric_name')} style={{ cursor: 'pointer', width: '25%' }}>\r\n                Fabric Name {getSortIcon('fabric_name')}\r\n              </th>\r\n              <th onClick={() => handleSort('supplier_name')} style={{ cursor: 'pointer', width: '20%' }}>\r\n                Supplier {getSortIcon('supplier_name')}\r\n              </th>\r\n              <th onClick={() => handleSort('date_added')} style={{ cursor: 'pointer', width: '15%' }}>\r\n                Date Added {getSortIcon('date_added')}\r\n              </th>\r\n              <th onClick={() => handleSort('variant_count')} style={{ cursor: 'pointer', width: '15%' }} className=\"text-center\">\r\n                Colors {getSortIcon('variant_count')}\r\n              </th>\r\n              <th style={{ width: '25%' }} className=\"text-center\">Actions</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {filteredFabrics.map((fabric) => (\r\n              <tr key={fabric.id} className=\"align-middle\">\r\n                <td className=\"text-center\">{fabric.id}</td>\r\n                <td>\r\n                  <div className=\"d-flex align-items-center\">\r\n                    <FaTshirt className=\"me-2 text-primary\" />\r\n                    <span\r\n                      style={{ cursor: 'pointer', fontWeight: 'bold' }}\r\n                      onClick={() => handleViewVariants(fabric.id)}\r\n                    >\r\n                      {fabric.fabric_name}\r\n                    </span>\r\n                  </div>\r\n                </td>\r\n                <td>\r\n                  <div className=\"d-flex align-items-center\">\r\n                    <FaUserTie className=\"me-2 text-secondary\" />\r\n                    {fabric.supplier_name}\r\n                  </div>\r\n                </td>\r\n                <td>\r\n                  <div className=\"d-flex align-items-center\">\r\n                    <FaCalendarAlt className=\"me-2 text-secondary\" />\r\n                    {formatDate(fabric.date_added)}\r\n                  </div>\r\n                </td>\r\n                <td className=\"text-center\">\r\n                  <Badge\r\n                    bg=\"primary\"\r\n                    pill\r\n                    style={{ fontSize: '0.9rem', padding: '0.4rem 0.8rem' }}\r\n                  >\r\n                    <FaPalette className=\"me-1\" /> {fabric.variant_count}\r\n                  </Badge>\r\n                </td>\r\n                <td className=\"text-center\">\r\n                  <div className=\"d-flex justify-content-center gap-2\">\r\n                    <Button\r\n                      variant=\"info\"\r\n                      className=\"btn-sm\"\r\n                      onClick={() => handleViewVariants(fabric.id)}\r\n                    >\r\n                      <FaEye className=\"me-1\" /> View\r\n                    </Button>\r\n\r\n                    {isInventoryManager && (\r\n                      <>\r\n                        <Button\r\n                          variant=\"warning\"\r\n                          className=\"btn-sm\"\r\n                          onClick={() => handleEditFabric(fabric.id)}\r\n                        >\r\n                          <FaEdit className=\"me-1\" /> Edit\r\n                        </Button>\r\n                        <Button\r\n                          variant=\"danger\"\r\n                          className=\"btn-sm\"\r\n                          onClick={() => handleDeleteClick(fabric)}\r\n                        >\r\n                          <FaTrash className=\"me-1\" /> Delete\r\n                        </Button>\r\n                      </>\r\n                    )}\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ))}\r\n          </tbody>\r\n        </Table>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <Container fluid\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n          <h2 className=\"mb-0\">\r\n            <FaTshirt className=\"me-2 text-primary\" />\r\n            Fabric Inventory\r\n          </h2>\r\n          {isInventoryManager && (\r\n            <Button\r\n              variant=\"success\"\r\n              onClick={() => navigate('/addfabric')}\r\n            >\r\n              Add New Fabric\r\n            </Button>\r\n          )}\r\n        </div>\r\n\r\n        {message && <Alert variant=\"success\" className=\"text-center\">{message}</Alert>}\r\n        {error && <Alert variant=\"danger\" className=\"text-center\">{error}</Alert>}\r\n        {success && <Alert variant=\"success\" className=\"text-center\">{success}</Alert>}\r\n\r\n        {/* Delete Confirmation Modal */}\r\n        <Modal show={showDeleteModal} onHide={closeDeleteModal} centered>\r\n          <Modal.Header closeButton>\r\n            <Modal.Title>Confirm Deletion</Modal.Title>\r\n          </Modal.Header>\r\n          <Modal.Body>\r\n            {fabricToDelete && (\r\n              <>\r\n                <p>Are you sure you want to delete this fabric?</p>\r\n                <Table bordered size=\"sm\" className=\"mt-3\">\r\n                  <tbody>\r\n                    <tr>\r\n                      <td><strong>Fabric Name:</strong></td>\r\n                      <td>{fabricToDelete.fabric_name}</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td><strong>Supplier:</strong></td>\r\n                      <td>{fabricToDelete.supplier_name}</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td><strong>Date Added:</strong></td>\r\n                      <td>{formatDate(fabricToDelete.date_added)}</td>\r\n                    </tr>\r\n                    <tr>\r\n                      <td><strong>Color Variants:</strong></td>\r\n                      <td>{fabricToDelete.variant_count}</td>\r\n                    </tr>\r\n                  </tbody>\r\n                </Table>\r\n\r\n                {hasDependencies && (\r\n                  <Alert variant=\"warning\" className=\"mt-3\">\r\n                    <strong>Warning:</strong> This fabric cannot be deleted because it or one of its color variants is being used in cutting records.\r\n                    <div className=\"mt-2\">\r\n                      <small>\r\n                        To delete this fabric, you must first delete all cutting records that use it or any of its color variants. You can view these records by clicking \"View\" to see the variants, then \"View Inventory\" for each variant to check its cutting history.\r\n                      </small>\r\n                    </div>\r\n                  </Alert>\r\n                )}\r\n\r\n                {!hasDependencies && (\r\n                  <Alert variant=\"success\" className=\"mt-3\">\r\n                    <strong>Good news!</strong> This fabric and all its variants have no cutting records and can be safely deleted.\r\n                  </Alert>\r\n                )}\r\n              </>\r\n            )}\r\n          </Modal.Body>\r\n          <Modal.Footer>\r\n            <Button variant=\"secondary\" onClick={closeDeleteModal}>\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              variant=\"danger\"\r\n              onClick={confirmDelete}\r\n              disabled={isDeleting || hasDependencies}\r\n            >\r\n              {isDeleting ? (\r\n                <>\r\n                  <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                  Deleting...\r\n                </>\r\n              ) : (\r\n                'Delete Fabric'\r\n              )}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Modal>\r\n\r\n        {/* Search and Filter Section */}\r\n        <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: '#f8f9fa' }}>\r\n          <Card.Body>\r\n            <Row className=\"g-3\">\r\n              {/* Search Bar */}\r\n              <Col lg={4} md={6} sm={12}>\r\n                <InputGroup>\r\n                  <InputGroup.Text className=\"bg-white\">\r\n                    <FaSearch />\r\n                  </InputGroup.Text>\r\n                  <Form.Control\r\n                    placeholder=\"Search by fabric name or supplier...\"\r\n                    value={searchTerm}\r\n                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                    className=\"border-start-0\"\r\n                  />\r\n                  {searchTerm && (\r\n                    <Button\r\n                      variant=\"outline-secondary\"\r\n                      onClick={() => setSearchTerm('')}\r\n                    >\r\n                      Clear\r\n                    </Button>\r\n                  )}\r\n                </InputGroup>\r\n              </Col>\r\n\r\n              {/* Supplier Filter */}\r\n              <Col lg={3} md={6} sm={12}>\r\n                <DropdownButton\r\n                  id=\"supplier-filter\"\r\n                  title={supplierFilter || \"Filter by Supplier\"}\r\n                  variant=\"outline-primary\"\r\n                  className=\"w-100\"\r\n                >\r\n                  <Dropdown.Item onClick={() => setSupplierFilter('')}>All Suppliers</Dropdown.Item>\r\n                  <Dropdown.Divider />\r\n                  {suppliers.map((supplier, index) => (\r\n                    <Dropdown.Item\r\n                      key={index}\r\n                      onClick={() => setSupplierFilter(supplier)}\r\n                      active={supplierFilter === supplier}\r\n                    >\r\n                      {supplier}\r\n                    </Dropdown.Item>\r\n                  ))}\r\n                </DropdownButton>\r\n              </Col>\r\n\r\n              {/* Date Range Filter */}\r\n              <Col lg={3} md={6} sm={12}>\r\n                <InputGroup>\r\n                  <InputGroup.Text className=\"bg-white\">\r\n                    <FaCalendarAlt />\r\n                  </InputGroup.Text>\r\n                  <Form.Control\r\n                    type=\"date\"\r\n                    placeholder=\"From\"\r\n                    value={startDate}\r\n                    onChange={(e) => setStartDate(e.target.value)}\r\n                    className=\"border-start-0\"\r\n                  />\r\n                  <Form.Control\r\n                    type=\"date\"\r\n                    placeholder=\"To\"\r\n                    value={endDate}\r\n                    onChange={(e) => setEndDate(e.target.value)}\r\n                  />\r\n                </InputGroup>\r\n              </Col>\r\n\r\n              {/* Color Count Range */}\r\n              <Col lg={2} md={6} sm={12}>\r\n                <InputGroup>\r\n                  <InputGroup.Text className=\"bg-white\">\r\n                    <FaPalette />\r\n                  </InputGroup.Text>\r\n                  <Form.Control\r\n                    type=\"number\"\r\n                    placeholder=\"Min\"\r\n                    min=\"0\"\r\n                    value={minColorCount}\r\n                    onChange={(e) => setMinColorCount(e.target.value)}\r\n                    className=\"border-start-0\"\r\n                  />\r\n                  <Form.Control\r\n                    type=\"number\"\r\n                    placeholder=\"Max\"\r\n                    min=\"0\"\r\n                    value={maxColorCount}\r\n                    onChange={(e) => setMaxColorCount(e.target.value)}\r\n                  />\r\n                </InputGroup>\r\n              </Col>\r\n            </Row>\r\n\r\n            <div className=\"d-flex justify-content-between mt-3\">\r\n              <div>\r\n                <Badge bg=\"primary\" className=\"me-2 p-2\">\r\n                  {filteredFabrics.length} fabrics found\r\n                </Badge>\r\n                {(searchTerm || supplierFilter || minColorCount || maxColorCount || startDate || endDate) && (\r\n                  <Button\r\n                    variant=\"outline-danger\"\r\n                    size=\"sm\"\r\n                    onClick={resetFilters}\r\n                  >\r\n                    Reset Filters\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <div>\r\n                <Form.Select\r\n                  size=\"sm\"\r\n                  style={{ width: 'auto', display: 'inline-block' }}\r\n                  value={`${sortField}-${sortOrder}`}\r\n                  onChange={(e) => {\r\n                    const [field, order] = e.target.value.split('-');\r\n                    setSortField(field);\r\n                    setSortOrder(order);\r\n                  }}\r\n                  className=\"border-primary\"\r\n                >\r\n                  <option value=\"date_added-desc\">Newest First</option>\r\n                  <option value=\"date_added-asc\">Oldest First</option>\r\n                  <option value=\"fabric_name-asc\">Name (A-Z)</option>\r\n                  <option value=\"fabric_name-desc\">Name (Z-A)</option>\r\n                  <option value=\"supplier_name-asc\">Supplier (A-Z)</option>\r\n                  <option value=\"supplier_name-desc\">Supplier (Z-A)</option>\r\n                  <option value=\"variant_count-desc\">Most Colors</option>\r\n                  <option value=\"variant_count-asc\">Fewest Colors</option>\r\n                </Form.Select>\r\n              </div>\r\n            </div>\r\n          </Card.Body>\r\n        </Card>\r\n\r\n        {/* Loading Spinner */}\r\n        {loading ? (\r\n          <div className=\"text-center my-5\">\r\n            <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </Spinner>\r\n            <p className=\"mt-2\">Loading fabrics...</p>\r\n          </div>\r\n        ) : filteredFabrics.length === 0 ? (\r\n          <Alert variant=\"info\" className=\"text-center\">\r\n            No fabrics found matching your filters. Try adjusting your search criteria.\r\n          </Alert>\r\n        ) : (\r\n          renderTableView()\r\n        )}\r\n      </Container>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ViewFabrics;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,OAAO,QAAQ,eAAe;AACpD,SACEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAC3CC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,cAAc,EACvCC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,QACvB,iBAAiB;AACxB,SACEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EACtCC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAC9BC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,OAAO,QAChC,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAACkD,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAC5E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAACK,WAAW,CAAC,CAAC,CAAC;EACvD,MAAM,CAACmD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzD,QAAQ,CAACM,OAAO,CAAC,mBAAmB,CAAC,CAAC;EAC1F,MAAMoD,QAAQ,GAAGtD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiE,eAAe,EAAEC,kBAAkB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACmE,UAAU,EAAEC,aAAa,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqE,SAAS,EAAEC,YAAY,CAAC,GAAGtE,QAAQ,CAAC,YAAY,CAAC;EACxD,MAAM,CAACuE,SAAS,EAAEC,YAAY,CAAC,GAAGxE,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACyE,cAAc,EAAEC,iBAAiB,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+E,SAAS,EAAEC,YAAY,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiF,OAAO,EAAEC,UAAU,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmF,SAAS,EAAEC,YAAY,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoF,YAAY,GAAGA,CAAA,KAAM;MACzBpC,gBAAgB,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5C,CAAC;IAEDD,MAAM,CAACoC,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMnC,MAAM,CAACqC,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApF,SAAS,CAAC,MAAM;IACd,MAAMuF,mBAAmB,GAAGA,CAAA,KAAM;MAChC,MAAMC,OAAO,GAAGpF,WAAW,CAAC,CAAC;MAC7B,IAAIoF,OAAO,KAAKnC,QAAQ,EAAE;QACxBC,WAAW,CAACkC,OAAO,CAAC;QACpBhC,qBAAqB,CAACgC,OAAO,KAAK,mBAAmB,CAAC;MACxD;IACF,CAAC;IAEDvC,MAAM,CAACoC,gBAAgB,CAAC,SAAS,EAAEE,mBAAmB,CAAC;IACvD,OAAO,MAAM;MACXtC,MAAM,CAACqC,mBAAmB,CAAC,SAAS,EAAEC,mBAAmB,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,CAAClC,QAAQ,CAAC,CAAC;;EAEd;EACArD,SAAS,CAAC,MAAM;IACdoD,UAAU,CAAC,IAAI,CAAC;IAChBnD,KAAK,CACFwF,GAAG,CAAC,+CAA+C,CAAC,CACpDC,IAAI,CAAEC,QAAQ,IAAK;MAClB,MAAMC,UAAU,GAAGD,QAAQ,CAACE,IAAI;MAChCvD,UAAU,CAACsD,UAAU,CAAC;MACtBpD,kBAAkB,CAACoD,UAAU,CAAC;;MAE9B;MACA,MAAME,YAAY,GAAGF,UAAU,CAACG,OAAO,CAACC,MAAM,IAAIA,MAAM,CAACC,cAAc,IAAI,EAAE,CAAC;MAC9E,MAAMC,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACL,YAAY,CAAC,CAAC;MAClDX,YAAY,CAACe,eAAe,CAAC;MAE7B9C,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDgD,KAAK,CAAEzD,KAAK,IAAK;MAChB0D,OAAO,CAAC1D,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CD,UAAU,CAAC,wBAAwB,CAAC;MACpCU,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApD,SAAS,CAAC,MAAM;IACd,IAAIsG,MAAM,GAAG,CAAC,GAAGjE,OAAO,CAAC;;IAEzB;IACA,IAAI6B,UAAU,EAAE;MACd,MAAMqC,IAAI,GAAGrC,UAAU,CAACsC,WAAW,CAAC,CAAC;MACrCF,MAAM,GAAGA,MAAM,CAACG,MAAM,CACpBT,MAAM,IACJA,MAAM,CAACU,WAAW,CAACF,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,IAAI,CAAC,IAC9CP,MAAM,CAACC,cAAc,IAAID,MAAM,CAACC,cAAc,CAACW,IAAI,CAACC,IAAI,IACvDA,IAAI,CAACL,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,IAAI,CAClC,CACJ,CAAC;IACH;;IAEA;IACA,IAAI/B,cAAc,EAAE;MAClB8B,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACT,MAAM,IAC3BA,MAAM,CAACC,cAAc,IAAID,MAAM,CAACC,cAAc,CAACU,QAAQ,CAACnC,cAAc,CACxE,CAAC;IACH;;IAEA;IACA,IAAIE,aAAa,KAAK,EAAE,EAAE;MACxB4B,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACT,MAAM,IAAIA,MAAM,CAACc,aAAa,IAAIC,QAAQ,CAACrC,aAAa,CAAC,CAAC;IACnF;IACA,IAAIE,aAAa,KAAK,EAAE,EAAE;MACxB0B,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACT,MAAM,IAAIA,MAAM,CAACc,aAAa,IAAIC,QAAQ,CAACnC,aAAa,CAAC,CAAC;IACnF;;IAEA;IACA,IAAIE,SAAS,EAAE;MACbwB,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACT,MAAM,IAAI,IAAIgB,IAAI,CAAChB,MAAM,CAACiB,UAAU,CAAC,IAAI,IAAID,IAAI,CAAClC,SAAS,CAAC,CAAC;IACtF;IACA,IAAIE,OAAO,EAAE;MACXsB,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACT,MAAM,IAAI,IAAIgB,IAAI,CAAChB,MAAM,CAACiB,UAAU,CAAC,IAAI,IAAID,IAAI,CAAChC,OAAO,CAAC,CAAC;IACpF;;IAEA;IACAsB,MAAM,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACpB,IAAIC,IAAI,GAAGF,CAAC,CAAC/C,SAAS,CAAC;MACvB,IAAIkD,IAAI,GAAGF,CAAC,CAAChD,SAAS,CAAC;;MAEvB;MACA,IAAIA,SAAS,KAAK,YAAY,EAAE;QAC9BiD,IAAI,GAAG,IAAIL,IAAI,CAACK,IAAI,CAAC;QACrBC,IAAI,GAAG,IAAIN,IAAI,CAACM,IAAI,CAAC;MACvB;MACA;MAAA,KACK,IAAIlD,SAAS,KAAK,eAAe,IAAIA,SAAS,KAAK,IAAI,EAAE;QAC5DiD,IAAI,GAAGN,QAAQ,CAACM,IAAI,CAAC;QACrBC,IAAI,GAAGP,QAAQ,CAACO,IAAI,CAAC;MACvB;MACA;MAAA,KACK;QACHD,IAAI,GAAGA,IAAI,CAACE,QAAQ,CAAC,CAAC,CAACf,WAAW,CAAC,CAAC;QACpCc,IAAI,GAAGA,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACf,WAAW,CAAC,CAAC;MACtC;MAEA,IAAIa,IAAI,GAAGC,IAAI,EAAE,OAAOhD,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MACpD,IAAI+C,IAAI,GAAGC,IAAI,EAAE,OAAOhD,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MACpD,OAAO,CAAC;IACV,CAAC,CAAC;IAEF9B,kBAAkB,CAAC8D,MAAM,CAAC;EAC5B,CAAC,EAAE,CAACjE,OAAO,EAAE6B,UAAU,EAAEE,SAAS,EAAEE,SAAS,EAAEE,cAAc,EAAEE,aAAa,EAAEE,aAAa,EAAEE,SAAS,EAAEE,OAAO,CAAC,CAAC;;EAEjH;EACA,MAAMwC,kBAAkB,GAAIC,QAAQ,IAAK;IACvChE,QAAQ,CAAC,uBAAuBgE,QAAQ,EAAE,CAAC;EAC7C,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIC,KAAK,IAAK;IAC5B,IAAIvD,SAAS,KAAKuD,KAAK,EAAE;MACvBpD,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IACpD,CAAC,MAAM;MACLD,YAAY,CAACsD,KAAK,CAAC;MACnBpD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMqD,WAAW,GAAID,KAAK,IAAK;IAC7B,IAAIvD,SAAS,KAAKuD,KAAK,EAAE,oBAAO3F,OAAA,CAACX,MAAM;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1C,OAAO1D,SAAS,KAAK,KAAK,gBAAGtC,OAAA,CAACV,QAAQ;MAAAuG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGhG,OAAA,CAACT,UAAU;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC5D,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB9D,aAAa,CAAC,EAAE,CAAC;IACjBM,iBAAiB,CAAC,EAAE,CAAC;IACrBE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,YAAY,CAAC,EAAE,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IACdZ,YAAY,CAAC,YAAY,CAAC;IAC1BE,YAAY,CAAC,MAAM,CAAC;EACtB,CAAC;;EAED;EACA,MAAM2D,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,OAAO,GAAG;MAAEC,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAU,CAAC;IACnE,OAAO,IAAIvB,IAAI,CAACmB,UAAU,CAAC,CAACK,kBAAkB,CAACC,SAAS,EAAEL,OAAO,CAAC;EACpE,CAAC;;EAED;EACA,MAAMM,gBAAgB,GAAIjB,QAAQ,IAAK;IACrChE,QAAQ,CAAC,gBAAgBgE,QAAQ,EAAE,CAAC;EACtC,CAAC;;EAED;EACA,MAAMkB,uBAAuB,GAAG,MAAOlB,QAAQ,IAAK;IAClD,IAAI;MACFpB,OAAO,CAACuC,GAAG,CAAC,+CAA+CnB,QAAQ,EAAE,CAAC;;MAEtE;MACA,MAAMoB,eAAe,GAAG,MAAM5I,KAAK,CAACwF,GAAG,CAAC,oDAAoD,CAAC;MAC7FY,OAAO,CAACuC,GAAG,CAAC,sBAAsB,EAAEC,eAAe,CAAChD,IAAI,CAAC;;MAEzD;MACA,MAAMiD,iBAAiB,GAAGD,eAAe,CAAChD,IAAI,CAACe,IAAI,CACjDmC,MAAM,IAAIA,MAAM,CAACC,iBAAiB,KAAKvB,QAAQ,IAAIsB,MAAM,CAACC,iBAAiB,KAAKjC,QAAQ,CAACU,QAAQ,CACnG,CAAC;MAED,IAAIqB,iBAAiB,EAAE;QACrBzC,OAAO,CAACuC,GAAG,CAAC,qBAAqBnB,QAAQ,sCAAsC,CAAC;QAChF,OAAO,IAAI;MACb;;MAEA;MACA,IAAI;QACF;QACA,MAAMwB,gBAAgB,GAAG,MAAMhJ,KAAK,CAACwF,GAAG,CAAC,gDAAgDgC,QAAQ,YAAY,CAAC;QAC9G,MAAMyB,QAAQ,GAAGD,gBAAgB,CAACpD,IAAI;QACtCQ,OAAO,CAACuC,GAAG,CAAC,SAASM,QAAQ,CAACC,MAAM,mCAAmC1B,QAAQ,GAAG,EAAEyB,QAAQ,CAAC;;QAE7F;QACA,KAAK,MAAMH,MAAM,IAAIF,eAAe,CAAChD,IAAI,EAAE;UACzC,IAAIkD,MAAM,CAACK,OAAO,IAAIL,MAAM,CAACK,OAAO,CAACD,MAAM,GAAG,CAAC,EAAE;YAC/C,KAAK,MAAME,OAAO,IAAIH,QAAQ,EAAE;cAC9B;cACA,MAAMI,WAAW,GAAGP,MAAM,CAACK,OAAO,CAACxC,IAAI,CACrC2C,MAAM,IAAIA,MAAM,CAACC,cAAc,KAAKH,OAAO,CAACI,EAAE,IAAIF,MAAM,CAACC,cAAc,KAAKzC,QAAQ,CAACsC,OAAO,CAACI,EAAE,CACjG,CAAC;cAED,IAAIH,WAAW,EAAE;gBACfjD,OAAO,CAACuC,GAAG,CAAC,WAAWS,OAAO,CAACI,EAAE,8BAA8BV,MAAM,CAACU,EAAE,EAAE,CAAC;gBAC3E,OAAO,IAAI;cACb;YACF;UACF;QACF;;QAEA;QACA,KAAK,MAAMJ,OAAO,IAAIH,QAAQ,EAAE;UAC9B,IAAI;YACF7C,OAAO,CAACuC,GAAG,CAAC,wCAAwCS,OAAO,CAACI,EAAE,EAAE,CAAC;YACjE,MAAMC,sBAAsB,GAAG,MAAMzJ,KAAK,CAACwF,GAAG,CAAC,oDAAoD4D,OAAO,CAACI,EAAE,WAAW,CAAC;YACzHpD,OAAO,CAACuC,GAAG,CAAC,WAAWS,OAAO,CAACI,EAAE,WAAW,EAAEC,sBAAsB,CAAC7D,IAAI,CAAC;YAE1E,IAAI6D,sBAAsB,CAAC7D,IAAI,IAC3B6D,sBAAsB,CAAC7D,IAAI,CAAC8D,eAAe,IAC3CD,sBAAsB,CAAC7D,IAAI,CAAC8D,eAAe,CAACR,MAAM,GAAG,CAAC,EAAE;cAC1D9C,OAAO,CAACuC,GAAG,CAAC,WAAWS,OAAO,CAACI,EAAE,QAAQC,sBAAsB,CAAC7D,IAAI,CAAC8D,eAAe,CAACR,MAAM,kBAAkB,CAAC;cAC9G,OAAO,IAAI,CAAC,CAAC;YACf;UACF,CAAC,CAAC,OAAOxG,KAAK,EAAE;YACd0D,OAAO,CAAC1D,KAAK,CAAC,8CAA8C0G,OAAO,CAACI,EAAE,GAAG,EAAE9G,KAAK,CAAC;;YAEjF;YACA;YACA,IAAIA,KAAK,CAACgD,QAAQ,IAAIhD,KAAK,CAACgD,QAAQ,CAACiE,MAAM,KAAK,GAAG,EAAE;cACnDvD,OAAO,CAACuC,GAAG,CAAC,WAAWS,OAAO,CAACI,EAAE,sCAAsC,CAAC;cACxE;YACF;;YAEA;YACApD,OAAO,CAAC1D,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACgD,QAAQ,GAAGhD,KAAK,CAACgD,QAAQ,CAACE,IAAI,GAAG,kBAAkB,CAAC;UAC5F;QACF;MACF,CAAC,CAAC,OAAOlD,KAAK,EAAE;QACd0D,OAAO,CAAC1D,KAAK,CAAC,iDAAiD8E,QAAQ,GAAG,EAAE9E,KAAK,CAAC;QAClF0D,OAAO,CAAC1D,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACgD,QAAQ,GAAGhD,KAAK,CAACgD,QAAQ,CAACE,IAAI,GAAG,kBAAkB,CAAC;;QAE1F;QACA,OAAO,IAAI;MACb;;MAEA;MACAQ,OAAO,CAACuC,GAAG,CAAC,kDAAkDnB,QAAQ,kBAAkB,CAAC;MACzF,OAAO,KAAK;IACd,CAAC,CAAC,OAAO9E,KAAK,EAAE;MACd0D,OAAO,CAAC1D,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D0D,OAAO,CAAC1D,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACgD,QAAQ,GAAGhD,KAAK,CAACgD,QAAQ,CAACE,IAAI,GAAG,kBAAkB,CAAC;;MAE1F;MACA,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAMgE,iBAAiB,GAAG,MAAO7D,MAAM,IAAK;IAC1CnC,iBAAiB,CAACmC,MAAM,CAAC;IACzBjC,aAAa,CAAC,KAAK,CAAC;;IAEpB;IACA,MAAM+F,OAAO,GAAG,MAAMnB,uBAAuB,CAAC3C,MAAM,CAACyD,EAAE,CAAC;IACxDxF,kBAAkB,CAAC6F,OAAO,CAAC;;IAE3B;IACAnG,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMoG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI/F,eAAe,EAAE;MACnB,OAAO,CAAC;IACV;IAEAD,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACFsC,OAAO,CAACuC,GAAG,CAAC,mDAAmDhF,cAAc,CAAC6F,EAAE,EAAE,CAAC;;MAEnF;MACA,IAAI;QACF;QACA,MAAMR,gBAAgB,GAAG,MAAMhJ,KAAK,CAACwF,GAAG,CAAC,gDAAgD7B,cAAc,CAAC6F,EAAE,YAAY,CAAC;QACvH,MAAMP,QAAQ,GAAGD,gBAAgB,CAACpD,IAAI;QACtCQ,OAAO,CAACuC,GAAG,CAAC,SAASM,QAAQ,CAACC,MAAM,6CAA6CvF,cAAc,CAAC6F,EAAE,EAAE,CAAC;;QAErG;QACA,KAAK,MAAMJ,OAAO,IAAIH,QAAQ,EAAE;UAC9B,IAAI;YACF7C,OAAO,CAACuC,GAAG,CAAC,gCAAgCS,OAAO,CAACI,EAAE,EAAE,CAAC;;YAEzD;YACA,MAAMxJ,KAAK,CAAC+J,GAAG,CAAC,6CAA6CX,OAAO,CAACI,EAAE,GAAG,EAAE;cAC1ET,iBAAiB,EAAEpF,cAAc,CAAC6F,EAAE;cACpCQ,KAAK,EAAEZ,OAAO,CAACY,KAAK;cACpBC,UAAU,EAAEb,OAAO,CAACa,UAAU,IAAIb,OAAO,CAACY,KAAK;cAC/CE,UAAU,EAAE,CAAC;cACbC,cAAc,EAAE,CAAC;cACjBC,cAAc,EAAE;YAClB,CAAC,CAAC;;YAEF;YACA,MAAMpK,KAAK,CAACqK,MAAM,CAAC,6CAA6CjB,OAAO,CAACI,EAAE,GAAG,CAAC;YAC9EpD,OAAO,CAACuC,GAAG,CAAC,gCAAgCS,OAAO,CAACI,EAAE,EAAE,CAAC;UAC3D,CAAC,CAAC,OAAOc,YAAY,EAAE;YACrBlE,OAAO,CAAC1D,KAAK,CAAC,0BAA0B0G,OAAO,CAACI,EAAE,GAAG,EAAEc,YAAY,CAAC;YACpE;UACF;QACF;MACF,CAAC,CAAC,OAAOC,aAAa,EAAE;QACtBnE,OAAO,CAAC1D,KAAK,CAAC,0BAA0B,EAAE6H,aAAa,CAAC;QACxD;MACF;;MAEA;MACA,MAAM7E,QAAQ,GAAG,MAAM1F,KAAK,CAACqK,MAAM,CAAC,gDAAgD1G,cAAc,CAAC6F,EAAE,GAAG,CAAC;MACzGpD,OAAO,CAACuC,GAAG,CAAC,kBAAkB,EAAEjD,QAAQ,CAAC;;MAEzC;MACA,MAAM8E,cAAc,GAAGpI,OAAO,CAACoE,MAAM,CAACiE,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAK7F,cAAc,CAAC6F,EAAE,CAAC;MACtEnH,UAAU,CAACmI,cAAc,CAAC;MAC1BjI,kBAAkB,CAACD,eAAe,CAACkE,MAAM,CAACiE,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAK7F,cAAc,CAAC6F,EAAE,CAAC,CAAC;;MAE3E;MACA9F,kBAAkB,CAAC,KAAK,CAAC;MACzBb,UAAU,CAAC,WAAWc,cAAc,CAAC8C,WAAW,8CAA8C,CAAC;;MAE/F;MACAiE,UAAU,CAAC,MAAM;QACf7H,UAAU,CAAC,EAAE,CAAC;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA,IAAAiI,eAAA,EAAAC,oBAAA;MACdxE,OAAO,CAAC1D,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA0D,OAAO,CAAC1D,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACgD,QAAQ,GAAGhD,KAAK,CAACgD,QAAQ,CAACE,IAAI,GAAG,kBAAkB,CAAC;MAC1FQ,OAAO,CAAC1D,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACgD,QAAQ,GAAGhD,KAAK,CAACgD,QAAQ,CAACiE,MAAM,GAAG,WAAW,CAAC;;MAEpF;MACA,IAAIkB,YAAY,GAAG,4CAA4C;MAE/D,IAAInI,KAAK,CAACgD,QAAQ,EAAE;QAClB,IAAIhD,KAAK,CAACgD,QAAQ,CAACiE,MAAM,KAAK,GAAG,EAAE;UACjCkB,YAAY,GAAGnI,KAAK,CAACgD,QAAQ,CAACE,IAAI,CAAC0D,MAAM,IAAI,6DAA6D;QAC5G,CAAC,MAAM,IAAI5G,KAAK,CAACgD,QAAQ,CAACiE,MAAM,KAAK,GAAG,EAAE;UACxCkB,YAAY,GAAG,kDAAkD;QACnE,CAAC,MAAM,IAAInI,KAAK,CAACgD,QAAQ,CAACiE,MAAM,KAAK,GAAG,EAAE;UACxCkB,YAAY,GAAG,qDAAqD;QACtE,CAAC,MAAM,IAAInI,KAAK,CAACgD,QAAQ,CAACiE,MAAM,KAAK,GAAG,EAAE;UACxCkB,YAAY,GAAG,iDAAiD;QAClE;MACF;MAEAlI,QAAQ,CAAC,EAAAgI,eAAA,GAAAjI,KAAK,CAACgD,QAAQ,cAAAiF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB/E,IAAI,cAAAgF,oBAAA,uBAApBA,oBAAA,CAAsBpI,OAAO,KAAIqI,YAAY,CAAC;;MAEvD;MACAH,UAAU,CAAC,MAAM;QACf/H,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,SAAS;MACRmB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMgH,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpH,kBAAkB,CAAC,KAAK,CAAC;IACzBE,iBAAiB,CAAC,IAAI,CAAC;IACvBI,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM+G,eAAe,GAAGA,CAAA,KAAM;IAC5B,oBACEhJ,OAAA;MAAKiJ,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BlJ,OAAA,CAACd,KAAK;QAACiK,KAAK;QAACC,QAAQ;QAACH,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACtDlJ,OAAA;UAAOiJ,SAAS,EAAC,UAAU;UAAAC,QAAA,eACzBlJ,OAAA;YAAAkJ,QAAA,gBACElJ,OAAA;cAAIqJ,OAAO,EAAEA,CAAA,KAAM3D,UAAU,CAAC,IAAI,CAAE;cAAC4D,KAAK,EAAE;gBAAEC,MAAM,EAAE,SAAS;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAACP,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAC,KACnG,EAACtD,WAAW,CAAC,IAAI,CAAC;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACLhG,OAAA;cAAIqJ,OAAO,EAAEA,CAAA,KAAM3D,UAAU,CAAC,aAAa,CAAE;cAAC4D,KAAK,EAAE;gBAAEC,MAAM,EAAE,SAAS;gBAAEC,KAAK,EAAE;cAAM,CAAE;cAAAN,QAAA,GAAC,cAC5E,EAACtD,WAAW,CAAC,aAAa,CAAC;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACLhG,OAAA;cAAIqJ,OAAO,EAAEA,CAAA,KAAM3D,UAAU,CAAC,eAAe,CAAE;cAAC4D,KAAK,EAAE;gBAAEC,MAAM,EAAE,SAAS;gBAAEC,KAAK,EAAE;cAAM,CAAE;cAAAN,QAAA,GAAC,WACjF,EAACtD,WAAW,CAAC,eAAe,CAAC;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACLhG,OAAA;cAAIqJ,OAAO,EAAEA,CAAA,KAAM3D,UAAU,CAAC,YAAY,CAAE;cAAC4D,KAAK,EAAE;gBAAEC,MAAM,EAAE,SAAS;gBAAEC,KAAK,EAAE;cAAM,CAAE;cAAAN,QAAA,GAAC,aAC5E,EAACtD,WAAW,CAAC,YAAY,CAAC;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACLhG,OAAA;cAAIqJ,OAAO,EAAEA,CAAA,KAAM3D,UAAU,CAAC,eAAe,CAAE;cAAC4D,KAAK,EAAE;gBAAEC,MAAM,EAAE,SAAS;gBAAEC,KAAK,EAAE;cAAM,CAAE;cAACP,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAC,SAC3G,EAACtD,WAAW,CAAC,eAAe,CAAC;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACLhG,OAAA;cAAIsJ,KAAK,EAAE;gBAAEE,KAAK,EAAE;cAAM,CAAE;cAACP,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAO;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRhG,OAAA;UAAAkJ,QAAA,EACG3I,eAAe,CAACkJ,GAAG,CAAEzF,MAAM,iBAC1BhE,OAAA;YAAoBiJ,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1ClJ,OAAA;cAAIiJ,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAElF,MAAM,CAACyD;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5ChG,OAAA;cAAAkJ,QAAA,eACElJ,OAAA;gBAAKiJ,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxClJ,OAAA,CAACP,QAAQ;kBAACwJ,SAAS,EAAC;gBAAmB;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1ChG,OAAA;kBACEsJ,KAAK,EAAE;oBAAEC,MAAM,EAAE,SAAS;oBAAEG,UAAU,EAAE;kBAAO,CAAE;kBACjDL,OAAO,EAAEA,CAAA,KAAM7D,kBAAkB,CAACxB,MAAM,CAACyD,EAAE,CAAE;kBAAAyB,QAAA,EAE5ClF,MAAM,CAACU;gBAAW;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLhG,OAAA;cAAAkJ,QAAA,eACElJ,OAAA;gBAAKiJ,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxClJ,OAAA,CAACL,SAAS;kBAACsJ,SAAS,EAAC;gBAAqB;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC5ChC,MAAM,CAAC2F,aAAa;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLhG,OAAA;cAAAkJ,QAAA,eACElJ,OAAA;gBAAKiJ,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxClJ,OAAA,CAACN,aAAa;kBAACuJ,SAAS,EAAC;gBAAqB;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChDE,UAAU,CAAClC,MAAM,CAACiB,UAAU,CAAC;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLhG,OAAA;cAAIiJ,SAAS,EAAC,aAAa;cAAAC,QAAA,eACzBlJ,OAAA,CAACnB,KAAK;gBACJ+K,EAAE,EAAC,SAAS;gBACZC,IAAI;gBACJP,KAAK,EAAE;kBAAEQ,QAAQ,EAAE,QAAQ;kBAAEC,OAAO,EAAE;gBAAgB,CAAE;gBAAAb,QAAA,gBAExDlJ,OAAA,CAACJ,SAAS;kBAACqJ,SAAS,EAAC;gBAAM;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAChC,MAAM,CAACc,aAAa;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACLhG,OAAA;cAAIiJ,SAAS,EAAC,aAAa;cAAAC,QAAA,eACzBlJ,OAAA;gBAAKiJ,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDlJ,OAAA,CAACpB,MAAM;kBACLyI,OAAO,EAAC,MAAM;kBACd4B,SAAS,EAAC,QAAQ;kBAClBI,OAAO,EAAEA,CAAA,KAAM7D,kBAAkB,CAACxB,MAAM,CAACyD,EAAE,CAAE;kBAAAyB,QAAA,gBAE7ClJ,OAAA,CAACR,KAAK;oBAACyJ,SAAS,EAAC;kBAAM;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,SAC5B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAERzE,kBAAkB,iBACjBvB,OAAA,CAAAE,SAAA;kBAAAgJ,QAAA,gBACElJ,OAAA,CAACpB,MAAM;oBACLyI,OAAO,EAAC,SAAS;oBACjB4B,SAAS,EAAC,QAAQ;oBAClBI,OAAO,EAAEA,CAAA,KAAM3C,gBAAgB,CAAC1C,MAAM,CAACyD,EAAE,CAAE;oBAAAyB,QAAA,gBAE3ClJ,OAAA,CAACH,MAAM;sBAACoJ,SAAS,EAAC;oBAAM;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,SAC7B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACThG,OAAA,CAACpB,MAAM;oBACLyI,OAAO,EAAC,QAAQ;oBAChB4B,SAAS,EAAC,QAAQ;oBAClBI,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAAC7D,MAAM,CAAE;oBAAAkF,QAAA,gBAEzClJ,OAAA,CAACF,OAAO;sBAACmJ,SAAS,EAAC;oBAAM;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,WAC9B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eACT,CACH;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GA/DEhC,MAAM,CAACyD,EAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgEd,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,oBACEhG,OAAA,CAAAE,SAAA;IAAAgJ,QAAA,gBACElJ,OAAA,CAAC9B,eAAe;MAAA2H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBhG,OAAA,CAAC1B,SAAS;MAAC0L,KAAK;MACdV,KAAK,EAAE;QACLW,UAAU,EAAElJ,aAAa,GAAG,OAAO,GAAG,MAAM;QAC5CyI,KAAK,EAAE,eAAezI,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG;QACzDmJ,UAAU,EAAE,eAAe;QAC3BH,OAAO,EAAE;MACX,CAAE;MAAAb,QAAA,gBAEFlJ,OAAA;QAAKiJ,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBACrElJ,OAAA;UAAIiJ,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAClBlJ,OAAA,CAACP,QAAQ;YAACwJ,SAAS,EAAC;UAAmB;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACJzE,kBAAkB,iBACjBvB,OAAA,CAACpB,MAAM;UACLyI,OAAO,EAAC,SAAS;UACjBgC,OAAO,EAAEA,CAAA,KAAM5H,QAAQ,CAAC,YAAY,CAAE;UAAAyH,QAAA,EACvC;QAED;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELvF,OAAO,iBAAIT,OAAA,CAACf,KAAK;QAACoI,OAAO,EAAC,SAAS;QAAC4B,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAEzI;MAAO;QAAAoF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAC7ErF,KAAK,iBAAIX,OAAA,CAACf,KAAK;QAACoI,OAAO,EAAC,QAAQ;QAAC4B,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAEvI;MAAK;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACxEnF,OAAO,iBAAIb,OAAA,CAACf,KAAK;QAACoI,OAAO,EAAC,SAAS;QAAC4B,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAErI;MAAO;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAG9EhG,OAAA,CAACb,KAAK;QAACgL,IAAI,EAAEzI,eAAgB;QAAC0I,MAAM,EAAErB,gBAAiB;QAACsB,QAAQ;QAAAnB,QAAA,gBAC9DlJ,OAAA,CAACb,KAAK,CAACmL,MAAM;UAACC,WAAW;UAAArB,QAAA,eACvBlJ,OAAA,CAACb,KAAK,CAACqL,KAAK;YAAAtB,QAAA,EAAC;UAAgB;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACfhG,OAAA,CAACb,KAAK,CAACsL,IAAI;UAAAvB,QAAA,EACRtH,cAAc,iBACb5B,OAAA,CAAAE,SAAA;YAAAgJ,QAAA,gBACElJ,OAAA;cAAAkJ,QAAA,EAAG;YAA4C;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDhG,OAAA,CAACd,KAAK;cAACkK,QAAQ;cAACsB,IAAI,EAAC,IAAI;cAACzB,SAAS,EAAC,MAAM;cAAAC,QAAA,eACxClJ,OAAA;gBAAAkJ,QAAA,gBACElJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAAkJ,QAAA,eAAIlJ,OAAA;sBAAAkJ,QAAA,EAAQ;oBAAY;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtChG,OAAA;oBAAAkJ,QAAA,EAAKtH,cAAc,CAAC8C;kBAAW;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACLhG,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAAkJ,QAAA,eAAIlJ,OAAA;sBAAAkJ,QAAA,EAAQ;oBAAS;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnChG,OAAA;oBAAAkJ,QAAA,EAAKtH,cAAc,CAAC+H;kBAAa;oBAAA9D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC,eACLhG,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAAkJ,QAAA,eAAIlJ,OAAA;sBAAAkJ,QAAA,EAAQ;oBAAW;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrChG,OAAA;oBAAAkJ,QAAA,EAAKhD,UAAU,CAACtE,cAAc,CAACqD,UAAU;kBAAC;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACLhG,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAAkJ,QAAA,eAAIlJ,OAAA;sBAAAkJ,QAAA,EAAQ;oBAAe;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzChG,OAAA;oBAAAkJ,QAAA,EAAKtH,cAAc,CAACkD;kBAAa;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEPhE,eAAe,iBACdhC,OAAA,CAACf,KAAK;cAACoI,OAAO,EAAC,SAAS;cAAC4B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACvClJ,OAAA;gBAAAkJ,QAAA,EAAQ;cAAQ;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,4GACzB,eAAAhG,OAAA;gBAAKiJ,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBlJ,OAAA;kBAAAkJ,QAAA,EAAO;gBAEP;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACR,EAEA,CAAChE,eAAe,iBACfhC,OAAA,CAACf,KAAK;cAACoI,OAAO,EAAC,SAAS;cAAC4B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACvClJ,OAAA;gBAAAkJ,QAAA,EAAQ;cAAU;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,wFAC7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA,eACD;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eACbhG,OAAA,CAACb,KAAK,CAACwL,MAAM;UAAAzB,QAAA,gBACXlJ,OAAA,CAACpB,MAAM;YAACyI,OAAO,EAAC,WAAW;YAACgC,OAAO,EAAEN,gBAAiB;YAAAG,QAAA,EAAC;UAEvD;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThG,OAAA,CAACpB,MAAM;YACLyI,OAAO,EAAC,QAAQ;YAChBgC,OAAO,EAAEtB,aAAc;YACvB6C,QAAQ,EAAE9I,UAAU,IAAIE,eAAgB;YAAAkH,QAAA,EAEvCpH,UAAU,gBACT9B,OAAA,CAAAE,SAAA;cAAAgJ,QAAA,gBACElJ,OAAA,CAAChB,OAAO;gBAAC6L,EAAE,EAAC,MAAM;gBAACC,SAAS,EAAC,QAAQ;gBAACJ,IAAI,EAAC,IAAI;gBAACK,IAAI,EAAC,QAAQ;gBAAC,eAAY,MAAM;gBAAC9B,SAAS,EAAC;cAAM;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEtG;YAAA,eAAE,CAAC,GAEH;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGRhG,OAAA,CAACvB,IAAI;QAACwK,SAAS,EAAC,gBAAgB;QAACK,KAAK,EAAE;UAAE0B,eAAe,EAAE;QAAU,CAAE;QAAA9B,QAAA,eACrElJ,OAAA,CAACvB,IAAI,CAACgM,IAAI;UAAAvB,QAAA,gBACRlJ,OAAA,CAACzB,GAAG;YAAC0K,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAElBlJ,OAAA,CAACxB,GAAG;cAACyM,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAAAjC,QAAA,eACxBlJ,OAAA,CAACrB,UAAU;gBAAAuK,QAAA,gBACTlJ,OAAA,CAACrB,UAAU,CAACyM,IAAI;kBAACnC,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACnClJ,OAAA,CAACZ,QAAQ;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eAClBhG,OAAA,CAACtB,IAAI,CAAC2M,OAAO;kBACXC,WAAW,EAAC,sCAAsC;kBAClDC,KAAK,EAAErJ,UAAW;kBAClBsJ,QAAQ,EAAGC,CAAC,IAAKtJ,aAAa,CAACsJ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CtC,SAAS,EAAC;gBAAgB;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,EACD9D,UAAU,iBACTlC,OAAA,CAACpB,MAAM;kBACLyI,OAAO,EAAC,mBAAmB;kBAC3BgC,OAAO,EAAEA,CAAA,KAAMlH,aAAa,CAAC,EAAE,CAAE;kBAAA+G,QAAA,EAClC;gBAED;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNhG,OAAA,CAACxB,GAAG;cAACyM,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAAAjC,QAAA,eACxBlJ,OAAA,CAACjB,cAAc;gBACb0I,EAAE,EAAC,iBAAiB;gBACpBkE,KAAK,EAAEnJ,cAAc,IAAI,oBAAqB;gBAC9C6E,OAAO,EAAC,iBAAiB;gBACzB4B,SAAS,EAAC,OAAO;gBAAAC,QAAA,gBAEjBlJ,OAAA,CAAClB,QAAQ,CAAC8M,IAAI;kBAACvC,OAAO,EAAEA,CAAA,KAAM5G,iBAAiB,CAAC,EAAE,CAAE;kBAAAyG,QAAA,EAAC;gBAAa;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAClFhG,OAAA,CAAClB,QAAQ,CAAC+M,OAAO;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACnB9C,SAAS,CAACuG,GAAG,CAAC,CAACqC,QAAQ,EAAEC,KAAK,kBAC7B/L,OAAA,CAAClB,QAAQ,CAAC8M,IAAI;kBAEZvC,OAAO,EAAEA,CAAA,KAAM5G,iBAAiB,CAACqJ,QAAQ,CAAE;kBAC3CE,MAAM,EAAExJ,cAAc,KAAKsJ,QAAS;kBAAA5C,QAAA,EAEnC4C;gBAAQ,GAJJC,KAAK;kBAAAlG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKG,CAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGNhG,OAAA,CAACxB,GAAG;cAACyM,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAAAjC,QAAA,eACxBlJ,OAAA,CAACrB,UAAU;gBAAAuK,QAAA,gBACTlJ,OAAA,CAACrB,UAAU,CAACyM,IAAI;kBAACnC,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACnClJ,OAAA,CAACN,aAAa;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAClBhG,OAAA,CAACtB,IAAI,CAAC2M,OAAO;kBACXY,IAAI,EAAC,MAAM;kBACXX,WAAW,EAAC,MAAM;kBAClBC,KAAK,EAAEzI,SAAU;kBACjB0I,QAAQ,EAAGC,CAAC,IAAK1I,YAAY,CAAC0I,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC9CtC,SAAS,EAAC;gBAAgB;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACFhG,OAAA,CAACtB,IAAI,CAAC2M,OAAO;kBACXY,IAAI,EAAC,MAAM;kBACXX,WAAW,EAAC,IAAI;kBAChBC,KAAK,EAAEvI,OAAQ;kBACfwI,QAAQ,EAAGC,CAAC,IAAKxI,UAAU,CAACwI,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAA1F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNhG,OAAA,CAACxB,GAAG;cAACyM,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAAAjC,QAAA,eACxBlJ,OAAA,CAACrB,UAAU;gBAAAuK,QAAA,gBACTlJ,OAAA,CAACrB,UAAU,CAACyM,IAAI;kBAACnC,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACnClJ,OAAA,CAACJ,SAAS;oBAAAiG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAClBhG,OAAA,CAACtB,IAAI,CAAC2M,OAAO;kBACXY,IAAI,EAAC,QAAQ;kBACbX,WAAW,EAAC,KAAK;kBACjBY,GAAG,EAAC,GAAG;kBACPX,KAAK,EAAE7I,aAAc;kBACrB8I,QAAQ,EAAGC,CAAC,IAAK9I,gBAAgB,CAAC8I,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAClDtC,SAAS,EAAC;gBAAgB;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACFhG,OAAA,CAACtB,IAAI,CAAC2M,OAAO;kBACXY,IAAI,EAAC,QAAQ;kBACbX,WAAW,EAAC,KAAK;kBACjBY,GAAG,EAAC,GAAG;kBACPX,KAAK,EAAE3I,aAAc;kBACrB4I,QAAQ,EAAGC,CAAC,IAAK5I,gBAAgB,CAAC4I,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAA1F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhG,OAAA;YAAKiJ,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBAClDlJ,OAAA;cAAAkJ,QAAA,gBACElJ,OAAA,CAACnB,KAAK;gBAAC+K,EAAE,EAAC,SAAS;gBAACX,SAAS,EAAC,UAAU;gBAAAC,QAAA,GACrC3I,eAAe,CAAC4G,MAAM,EAAC,gBAC1B;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACP,CAAC9D,UAAU,IAAIM,cAAc,IAAIE,aAAa,IAAIE,aAAa,IAAIE,SAAS,IAAIE,OAAO,kBACtFhD,OAAA,CAACpB,MAAM;gBACLyI,OAAO,EAAC,gBAAgB;gBACxBqD,IAAI,EAAC,IAAI;gBACTrB,OAAO,EAAEpD,YAAa;gBAAAiD,QAAA,EACvB;cAED;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNhG,OAAA;cAAAkJ,QAAA,eACElJ,OAAA,CAACtB,IAAI,CAACyN,MAAM;gBACVzB,IAAI,EAAC,IAAI;gBACTpB,KAAK,EAAE;kBAAEE,KAAK,EAAE,MAAM;kBAAE4C,OAAO,EAAE;gBAAe,CAAE;gBAClDb,KAAK,EAAE,GAAGnJ,SAAS,IAAIE,SAAS,EAAG;gBACnCkJ,QAAQ,EAAGC,CAAC,IAAK;kBACf,MAAM,CAAC9F,KAAK,EAAE0G,KAAK,CAAC,GAAGZ,CAAC,CAACC,MAAM,CAACH,KAAK,CAACe,KAAK,CAAC,GAAG,CAAC;kBAChDjK,YAAY,CAACsD,KAAK,CAAC;kBACnBpD,YAAY,CAAC8J,KAAK,CAAC;gBACrB,CAAE;gBACFpD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAE1BlJ,OAAA;kBAAQuL,KAAK,EAAC,iBAAiB;kBAAArC,QAAA,EAAC;gBAAY;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrDhG,OAAA;kBAAQuL,KAAK,EAAC,gBAAgB;kBAAArC,QAAA,EAAC;gBAAY;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDhG,OAAA;kBAAQuL,KAAK,EAAC,iBAAiB;kBAAArC,QAAA,EAAC;gBAAU;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnDhG,OAAA;kBAAQuL,KAAK,EAAC,kBAAkB;kBAAArC,QAAA,EAAC;gBAAU;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDhG,OAAA;kBAAQuL,KAAK,EAAC,mBAAmB;kBAAArC,QAAA,EAAC;gBAAc;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzDhG,OAAA;kBAAQuL,KAAK,EAAC,oBAAoB;kBAAArC,QAAA,EAAC;gBAAc;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1DhG,OAAA;kBAAQuL,KAAK,EAAC,oBAAoB;kBAAArC,QAAA,EAAC;gBAAW;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvDhG,OAAA;kBAAQuL,KAAK,EAAC,mBAAmB;kBAAArC,QAAA,EAAC;gBAAa;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,EAGN7E,OAAO,gBACNnB,OAAA;QAAKiJ,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BlJ,OAAA,CAAChB,OAAO;UAAC8L,SAAS,EAAC,QAAQ;UAACC,IAAI,EAAC,QAAQ;UAAC1D,OAAO,EAAC,SAAS;UAAA6B,QAAA,eACzDlJ,OAAA;YAAMiJ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACVhG,OAAA;UAAGiJ,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAkB;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,GACJzF,eAAe,CAAC4G,MAAM,KAAK,CAAC,gBAC9BnH,OAAA,CAACf,KAAK;QAACoI,OAAO,EAAC,MAAM;QAAC4B,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE9C;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,GAERgD,eAAe,CAAC,CACjB;IAAA;MAAAnD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA,eACZ,CAAC;AAEP,CAAC;AAAC5F,EAAA,CA/uBID,WAAW;EAAA,QAUEhC,WAAW;AAAA;AAAAoO,EAAA,GAVxBpM,WAAW;AAivBjB,eAAeA,WAAW;AAAC,IAAAoM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}