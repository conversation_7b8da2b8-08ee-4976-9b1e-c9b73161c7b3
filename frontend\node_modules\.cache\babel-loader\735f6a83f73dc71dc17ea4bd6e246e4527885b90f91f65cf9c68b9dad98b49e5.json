{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\Pri_Fashion_\\\\frontend\\\\src\\\\pages\\\\ViewFabricVariants.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\nimport { getUserRole, hasRole } from '../utils/auth';\nimport { Container, Row, Col, Card, Table, Button, Badge, Alert, Spinner, ListGroup, ProgressBar, Modal, Form } from 'react-bootstrap';\nimport { FaTshirt, FaArrowLeft, FaPalette, FaRulerHorizontal, FaMoneyBillWave, FaUserTie, FaCalendarAlt, FaInfoCircle, FaTrash, FaEdit, FaFilePdf, FaFileDownload, FaTable } from 'react-icons/fa';\nimport { jsPDF } from \"jspdf\";\nimport autoTable from 'jspdf-autotable';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ViewFabricVariants = () => {\n  _s();\n  const {\n    id\n  } = useParams(); // FabricDefinition ID from URL\n  const [fabricDetail, setFabricDetail] = useState(null);\n  const [message, setMessage] = useState(\"\");\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n  const [loading, setLoading] = useState(true);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\n  const [isInventoryManager, setIsInventoryManager] = useState(hasRole('Inventory Manager'));\n  const navigate = useNavigate();\n\n  // Delete confirmation modal states\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [variantToDelete, setVariantToDelete] = useState(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [hasDependencies, setHasDependencies] = useState(false);\n\n  // PDF export states\n  const [showPdfModal, setShowPdfModal] = useState(false);\n  const [pdfLoading, setPdfLoading] = useState(false);\n  const [showAdditionPdfModal, setShowAdditionPdfModal] = useState(false);\n  const [additionPdfLoading, setAdditionPdfLoading] = useState(false);\n\n  // Add resize event listener to update sidebar state\n  useEffect(() => {\n    const handleResize = () => {\n      setIsSidebarOpen(window.innerWidth >= 768);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n  useEffect(() => {\n    setLoading(true);\n    axios.get(`http://localhost:8000/api/fabric-definitions/${id}/`).then(response => {\n      setFabricDetail(response.data);\n      setLoading(false);\n    }).catch(error => {\n      console.error(\"Error fetching fabric definition:\", error);\n      setMessage(\"Error loading fabric details.\");\n      setLoading(false);\n    });\n  }, [id]);\n\n  // Format date for display\n  const formatDate = dateString => {\n    const options = {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    };\n    return new Date(dateString).toLocaleDateString(undefined, options);\n  };\n\n  // Calculate total inventory value\n  const calculateTotalValue = () => {\n    if (!fabricDetail || !fabricDetail.variants) return 0;\n    return fabricDetail.variants.reduce((total, variant) => {\n      return total + variant.total_yard * variant.price_per_yard;\n    }, 0).toFixed(2);\n  };\n\n  // Calculate current inventory value (based on available yards)\n  const calculateCurrentValue = () => {\n    if (!fabricDetail || !fabricDetail.variants) return 0;\n    return fabricDetail.variants.reduce((total, variant) => {\n      return total + variant.available_yard * variant.price_per_yard;\n    }, 0).toFixed(2);\n  };\n\n  // Calculate total yards\n  const calculateTotalYards = () => {\n    if (!fabricDetail || !fabricDetail.variants) return 0;\n    return fabricDetail.variants.reduce((total, variant) => {\n      return total + parseFloat(variant.total_yard);\n    }, 0).toFixed(2);\n  };\n\n  // Check if variant has any cutting records associated with it\n  const checkVariantDependencies = async variantId => {\n    try {\n      // Direct check for cutting records that use this fabric variant\n      // This is more reliable than using the history endpoint\n      const response = await axios.get(`http://localhost:8000/api/cutting/cutting-records/`);\n      console.log(\"All cutting records:\", response.data);\n\n      // Check if any cutting record details reference this variant\n      let hasReferences = false;\n      for (const record of response.data) {\n        if (record.details && record.details.length > 0) {\n          // Check if any detail uses this variant\n          const usesVariant = record.details.some(detail => detail.fabric_variant === variantId);\n          if (usesVariant) {\n            console.log(`Variant ${variantId} is used in cutting record ${record.id}`);\n            hasReferences = true;\n            break;\n          }\n        }\n      }\n      if (hasReferences) {\n        return true;\n      }\n\n      // Fallback to the history endpoint if needed\n      try {\n        const historyResponse = await axios.get(`http://localhost:8000/api/cutting/fabric-variant/${variantId}/history/`);\n        console.log(\"Variant dependency check response:\", historyResponse.data);\n\n        // If there are cutting records in the history, the variant has dependencies\n        if (historyResponse.data && historyResponse.data.cutting_history && historyResponse.data.cutting_history.length > 0) {\n          console.log(`Variant ${variantId} has ${historyResponse.data.cutting_history.length} cutting records`);\n          return true;\n        }\n      } catch (historyError) {\n        console.error(\"Error checking variant history:\", historyError);\n        // Continue with the main function even if history check fails\n      }\n\n      // No cutting records found - variant can be safely deleted\n      console.log(`Variant ${variantId} has no cutting records`);\n      return false;\n    } catch (error) {\n      console.error(\"Error checking variant dependencies:\", error);\n\n      // If it's a 404 error, it means the endpoint doesn't exist or the variant doesn't exist\n      // In this case, we can assume there are no dependencies\n      if (error.response && error.response.status === 404) {\n        console.log(`Variant ${variantId} not found or endpoint not available`);\n        return false;\n      }\n\n      // For other errors, log more details to help with debugging\n      console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\n\n      // If there's an error, assume there might be dependencies to prevent accidental deletion\n      return true;\n    }\n  };\n\n  // Handle delete button click\n  const handleDeleteClick = async variant => {\n    setVariantToDelete(variant);\n    setIsDeleting(false);\n    setHasDependencies(false); // Default to no dependencies\n\n    try {\n      // Check if this is the last variant\n      if (!fabricDetail.variants || fabricDetail.variants.length <= 1) {\n        setError(\"Cannot delete the last variant of a fabric. Please delete the entire fabric definition instead.\");\n        return;\n      }\n\n      // Check for dependencies\n      const hasDeps = await checkVariantDependencies(variant.id);\n      setHasDependencies(hasDeps);\n\n      // Show the confirmation modal\n      setShowDeleteModal(true);\n    } catch (error) {\n      console.error(\"Error in handleDeleteClick:\", error);\n      setError(\"An error occurred while checking if this variant can be deleted. Please try again.\");\n    }\n  };\n\n  // Handle delete confirmation\n  const confirmDelete = async () => {\n    if (hasDependencies) {\n      return; // Don't allow deletion if there are dependencies\n    }\n    setIsDeleting(true);\n    try {\n      console.log(`Attempting to delete variant with ID: ${variantToDelete.id}`);\n\n      // Double-check if this is the last variant\n      if (!fabricDetail.variants || fabricDetail.variants.length <= 1) {\n        setError(\"Cannot delete the last variant of a fabric. Please delete the entire fabric definition instead.\");\n        setIsDeleting(false);\n        return;\n      }\n\n      // Force a direct delete without checking dependencies again\n      try {\n        const response = await axios.delete(`http://localhost:8000/api/fabric-variants/${variantToDelete.id}/`);\n        console.log(\"Delete response:\", response);\n\n        // Update the fabric detail by removing the deleted variant\n        const updatedVariants = fabricDetail.variants.filter(v => v.id !== variantToDelete.id);\n        setFabricDetail({\n          ...fabricDetail,\n          variants: updatedVariants\n        });\n\n        // Close the modal and show success message\n        setShowDeleteModal(false);\n        setSuccess(`Fabric variant \"${variantToDelete.color_name || variantToDelete.color}\" deleted successfully!`);\n\n        // Clear success message after 3 seconds\n        setTimeout(() => {\n          setSuccess(\"\");\n        }, 3000);\n      } catch (deleteError) {\n        console.error(\"Error in direct delete attempt:\", deleteError);\n\n        // If the direct delete fails, try a workaround for variants with no cutting records\n        if (deleteError.response && (deleteError.response.status === 400 || deleteError.response.status === 500)) {\n          console.log(\"Attempting alternative delete method...\");\n          try {\n            // Update the variant with minimal data before deleting\n            await axios.put(`http://localhost:8000/api/fabric-variants/${variantToDelete.id}/`, {\n              fabric_definition: variantToDelete.fabric_definition,\n              color: variantToDelete.color,\n              color_name: variantToDelete.color_name || variantToDelete.color,\n              total_yard: 0,\n              available_yard: 0,\n              price_per_yard: 0\n            });\n\n            // Try deleting again\n            await axios.delete(`http://localhost:8000/api/fabric-variants/${variantToDelete.id}/`);\n\n            // Update the fabric detail by removing the deleted variant\n            const updatedVariants = fabricDetail.variants.filter(v => v.id !== variantToDelete.id);\n            setFabricDetail({\n              ...fabricDetail,\n              variants: updatedVariants\n            });\n\n            // Close the modal and show success message\n            setShowDeleteModal(false);\n            setSuccess(`Fabric variant \"${variantToDelete.color_name || variantToDelete.color}\" deleted successfully!`);\n\n            // Clear success message after 3 seconds\n            setTimeout(() => {\n              setSuccess(\"\");\n            }, 3000);\n          } catch (alternativeError) {\n            console.error(\"Alternative delete method failed:\", alternativeError);\n            throw alternativeError; // Re-throw to be caught by the outer catch block\n          }\n        } else {\n          throw deleteError; // Re-throw to be caught by the outer catch block\n        }\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Error deleting fabric variant:\", error);\n\n      // Log detailed error information\n      console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\n      console.error(\"Error status:\", error.response ? error.response.status : \"No status\");\n\n      // Provide more specific error messages based on the error\n      let errorMessage = \"Failed to delete fabric variant. Please try again.\";\n      if (error.response) {\n        if (error.response.status === 400) {\n          errorMessage = error.response.data.detail || \"Bad request. The variant may be referenced by other records.\";\n        } else if (error.response.status === 403) {\n          errorMessage = \"You don't have permission to delete this variant.\";\n        } else if (error.response.status === 404) {\n          errorMessage = \"Variant not found. It may have been already deleted.\";\n        } else if (error.response.status === 500) {\n          errorMessage = \"Server error. Please contact the administrator.\";\n        }\n      }\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || errorMessage);\n\n      // Clear error after 5 seconds\n      setTimeout(() => {\n        setError(\"\");\n      }, 5000);\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  // Close the delete modal\n  const closeDeleteModal = () => {\n    setShowDeleteModal(false);\n    setVariantToDelete(null);\n    setHasDependencies(false);\n  };\n\n  // Open Real-time Inventory PDF export modal\n  const openPdfModal = () => {\n    setShowPdfModal(true);\n  };\n\n  // Open Fabric Addition Report PDF modal\n  const openAdditionPdfModal = () => {\n    setShowAdditionPdfModal(true);\n  };\n\n  // Generate and download Real-time Inventory PDF file\n  const generatePDF = () => {\n    setPdfLoading(true);\n    if (!fabricDetail || !fabricDetail.variants) {\n      setError(\"No fabric data available to export\");\n      setPdfLoading(false);\n      return;\n    }\n    try {\n      // Create a new jsPDF instance\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n      });\n\n      // Set font sizes and styles\n      const titleFontSize = 18;\n      const headingFontSize = 14;\n      const normalFontSize = 10;\n      const smallFontSize = 8;\n\n      // Add logo\n      try {\n        // Get the base URL for the current environment\n        const baseUrl = window.location.origin;\n\n        // Add the logo to the PDF\n        doc.addImage(`${baseUrl}/logo.png`, 'PNG', 14, 10, 20, 20);\n      } catch (logoError) {\n        console.warn(\"Could not add logo to PDF:\", logoError);\n\n        // Fallback to a simple placeholder if the logo can't be loaded\n        doc.setFillColor(41, 128, 185); // Primary blue color\n        doc.rect(14, 10, 20, 20, 'F');\n\n        // Add \"PF\" text as a simple logo\n        doc.setFontSize(14);\n        doc.setTextColor(255, 255, 255);\n        doc.text(\"PF\", 24, 22, {\n          align: 'center'\n        });\n        doc.setTextColor(0, 0, 0); // Reset text color to black\n      }\n\n      // Add title\n      doc.setFontSize(titleFontSize);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Real-time Fabric Inventory Report', 105, 20, {\n        align: 'center'\n      });\n\n      // Add fabric information section\n      doc.setFontSize(headingFontSize);\n      doc.text('Fabric Information', 20, 35);\n      doc.setFontSize(normalFontSize);\n      doc.setFont('helvetica', 'normal');\n\n      // Add fabric details\n      const fabricInfo = [['Fabric Name', fabricDetail.fabric_name || 'N/A'], ['Suppliers', fabricDetail.supplier_names && fabricDetail.supplier_names.length > 0 ? fabricDetail.supplier_names.join(', ') : 'No suppliers'], ['Date Added', formatDate(fabricDetail.date_added) || 'N/A'], ['Total Variants', (fabricDetail.variants ? fabricDetail.variants.length : 0).toString()], ['Total Yards', calculateTotalYards().toString()], ['Available Yards', (fabricDetail.variants || []).reduce((total, variant) => total + parseFloat(variant.available_yard), 0).toFixed(2)], ['Total Value (All)', `Rs. ${calculateTotalValue()}`], ['Current Value', `Rs. ${calculateCurrentValue()}`]];\n\n      // Add fabric info table\n      autoTable(doc, {\n        startY: 40,\n        head: [['Property', 'Value']],\n        body: fabricInfo,\n        theme: 'grid',\n        headStyles: {\n          fillColor: [41, 128, 185],\n          textColor: 255\n        },\n        styles: {\n          fontSize: 10\n        }\n      });\n\n      // Add variants section\n      doc.setFontSize(headingFontSize);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Color Variants', 20, doc.lastAutoTable.finalY + 15);\n\n      // Prepare data for variants table\n      const variantsData = (fabricDetail.variants || []).map(variant => {\n        // Calculate current value based on available yard * price per yard\n        const currentValue = (variant.available_yard * variant.price_per_yard).toFixed(2);\n        return [variant.color_name || variant.color || 'N/A', '',\n        // Empty cell for color display\n        variant.color || 'N/A',\n        // Color code in separate column\n        variant.total_yard.toString(), variant.available_yard.toString(), `Rs. ${variant.price_per_yard}`, `Rs. ${currentValue}`];\n      });\n\n      // No need to define dimensions here as we'll use the cell dimensions\n\n      // Add variants table\n      autoTable(doc, {\n        startY: doc.lastAutoTable.finalY + 20,\n        head: [['Color Name', 'Color', 'Color Code', 'Total Yard', 'Available Yard', 'Price/Yard', 'Current Value']],\n        body: variantsData,\n        theme: 'striped',\n        styles: {\n          fontSize: 10\n        },\n        headStyles: {\n          fillColor: [41, 128, 185],\n          textColor: 255\n        },\n        columnStyles: {\n          1: {\n            cellWidth: 30\n          },\n          // Wider column for color swatch\n          2: {\n            cellWidth: 30\n          } // Wider column for color code\n        },\n        didDrawCell: data => {\n          // Add color box in the color display column (index 1)\n          if (data.section === 'body' && data.column.index === 1) {\n            const rowIndex = data.row.index;\n            const colorCode = variantsData[rowIndex][2]; // Get color code from the next column\n\n            // Only draw if it's a valid color code\n            if (colorCode && colorCode !== 'N/A') {\n              const {\n                x,\n                y,\n                width,\n                height\n              } = data.cell;\n\n              // Create a color swatch that fits within the cell with some padding\n              const padding = 2;\n              const swatchX = x + padding;\n              const swatchY = y + padding;\n              const swatchWidth = width - padding * 2;\n              const swatchHeight = height - padding * 2;\n\n              // Draw the color swatch\n              doc.setFillColor(colorCode);\n              doc.rect(swatchX, swatchY, swatchWidth, swatchHeight, 'F');\n\n              // Add border around the color box\n              doc.setDrawColor(100, 100, 100); // Darker border for better visibility\n              doc.rect(swatchX, swatchY, swatchWidth, swatchHeight, 'S');\n            }\n          }\n        }\n      });\n\n      // Add footer\n      doc.setFontSize(smallFontSize);\n      doc.setFont('helvetica', 'italic');\n      doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, {\n        align: 'center'\n      });\n      doc.text('Pri Fashion Garment Management System', 105, 285, {\n        align: 'center'\n      });\n\n      // Save the PDF\n      const cleanFabricName = fabricDetail.fabric_name.replace(/[^a-zA-Z0-9]/g, '_');\n      doc.save(`Fabric_Inventory_${cleanFabricName}_${new Date().toISOString().slice(0, 10)}.pdf`);\n      setPdfLoading(false);\n      setShowPdfModal(false);\n    } catch (error) {\n      console.error(\"Error generating PDF:\", error);\n      setError(`Failed to generate PDF: ${error.message}`);\n      setPdfLoading(false);\n    }\n  };\n\n  // Generate and download Fabric Addition Report PDF\n  const generateAdditionPDF = () => {\n    setAdditionPdfLoading(true);\n    if (!fabricDetail || !fabricDetail.variants) {\n      setError(\"No fabric data available to export\");\n      setAdditionPdfLoading(false);\n      return;\n    }\n    try {\n      // Create a new jsPDF instance\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n      });\n\n      // Set font sizes and styles\n      const titleFontSize = 18;\n      const headingFontSize = 14;\n      const normalFontSize = 10;\n      const smallFontSize = 8;\n\n      // Add logo\n      try {\n        // Get the base URL for the current environment\n        const baseUrl = window.location.origin;\n\n        // Add the logo to the PDF\n        doc.addImage(`${baseUrl}/logo.png`, 'PNG', 14, 10, 20, 20);\n      } catch (logoError) {\n        console.warn(\"Could not add logo to PDF:\", logoError);\n\n        // Fallback to a simple placeholder if the logo can't be loaded\n        doc.setFillColor(41, 128, 185); // Primary blue color\n        doc.rect(14, 10, 20, 20, 'F');\n\n        // Add \"PF\" text as a simple logo\n        doc.setFontSize(14);\n        doc.setTextColor(255, 255, 255);\n        doc.text(\"PF\", 24, 22, {\n          align: 'center'\n        });\n        doc.setTextColor(0, 0, 0); // Reset text color to black\n      }\n\n      // Add title\n      doc.setFontSize(titleFontSize);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Fabric Addition Report', 105, 20, {\n        align: 'center'\n      });\n\n      // Add fabric information section\n      doc.setFontSize(headingFontSize);\n      doc.text('Fabric Information', 20, 35);\n      doc.setFontSize(normalFontSize);\n      doc.setFont('helvetica', 'normal');\n\n      // Add fabric details\n      const fabricInfo = [['Fabric Name', fabricDetail.fabric_name || 'N/A'], ['Supplier', fabricDetail.supplier_name || 'N/A'], ['Date Added', formatDate(fabricDetail.date_added) || 'N/A'], ['Total Variants', fabricDetail.variants.length.toString()]];\n\n      // Add fabric info table\n      autoTable(doc, {\n        startY: 40,\n        head: [['Property', 'Value']],\n        body: fabricInfo,\n        theme: 'grid',\n        headStyles: {\n          fillColor: [41, 128, 185],\n          textColor: 255\n        },\n        styles: {\n          fontSize: 10\n        }\n      });\n\n      // Add variants section\n      doc.setFontSize(headingFontSize);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Added Fabric Variants', 20, doc.lastAutoTable.finalY + 15);\n\n      // Prepare data for variants table\n      const variantsData = (fabricDetail.variants || []).map(variant => {\n        return [variant.color_name || variant.color || 'N/A', '',\n        // Empty cell for color display\n        variant.color || 'N/A',\n        // Color code in separate column\n        variant.total_yard.toString(), `Rs. ${variant.price_per_yard}`, `Rs. ${(variant.total_yard * variant.price_per_yard).toFixed(2)}`];\n      });\n\n      // No need to define dimensions here as we'll use the cell dimensions\n\n      // Add variants table\n      autoTable(doc, {\n        startY: doc.lastAutoTable.finalY + 20,\n        head: [['Color Name', 'Color', 'Color Code', 'Added Yard', 'Price/Yard', 'Total Value']],\n        body: variantsData,\n        theme: 'striped',\n        styles: {\n          fontSize: 10\n        },\n        headStyles: {\n          fillColor: [41, 128, 185],\n          textColor: 255\n        },\n        columnStyles: {\n          1: {\n            cellWidth: 30\n          },\n          // Wider column for color swatch\n          2: {\n            cellWidth: 30\n          } // Wider column for color code\n        },\n        didDrawCell: data => {\n          // Add color box in the color display column (index 1)\n          if (data.section === 'body' && data.column.index === 1) {\n            const rowIndex = data.row.index;\n            const colorCode = variantsData[rowIndex][2]; // Get color code from the next column\n\n            // Only draw if it's a valid color code\n            if (colorCode && colorCode !== 'N/A') {\n              const {\n                x,\n                y,\n                width,\n                height\n              } = data.cell;\n\n              // Create a color swatch that fits within the cell with some padding\n              const padding = 2;\n              const swatchX = x + padding;\n              const swatchY = y + padding;\n              const swatchWidth = width - padding * 2;\n              const swatchHeight = height - padding * 2;\n\n              // Draw the color swatch\n              doc.setFillColor(colorCode);\n              doc.rect(swatchX, swatchY, swatchWidth, swatchHeight, 'F');\n\n              // Add border around the color box\n              doc.setDrawColor(100, 100, 100); // Darker border for better visibility\n              doc.rect(swatchX, swatchY, swatchWidth, swatchHeight, 'S');\n            }\n          }\n        }\n      });\n\n      // Add summary section\n      doc.setFontSize(headingFontSize);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Summary', 20, doc.lastAutoTable.finalY + 15);\n\n      // Calculate totals\n      const totalYards = calculateTotalYards();\n      const totalValue = calculateTotalValue();\n\n      // Add summary table\n      autoTable(doc, {\n        startY: doc.lastAutoTable.finalY + 20,\n        head: [['Total Yards Added', 'Total Value']],\n        body: [[totalYards, `Rs. ${totalValue}`]],\n        theme: 'grid',\n        styles: {\n          fontSize: 10,\n          halign: 'center'\n        },\n        headStyles: {\n          fillColor: [41, 128, 185],\n          textColor: 255\n        }\n      });\n\n      // Add footer\n      doc.setFontSize(smallFontSize);\n      doc.setFont('helvetica', 'italic');\n      doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, {\n        align: 'center'\n      });\n      doc.text('Pri Fashion Garment Management System', 105, 285, {\n        align: 'center'\n      });\n\n      // Save the PDF\n      const cleanFabricName = fabricDetail.fabric_name.replace(/[^a-zA-Z0-9]/g, '_');\n      doc.save(`Fabric_Addition_${cleanFabricName}_${new Date().toISOString().slice(0, 10)}.pdf`);\n      setAdditionPdfLoading(false);\n      setShowAdditionPdfModal(false);\n    } catch (error) {\n      console.error(\"Error generating PDF:\", error);\n      setError(`Failed to generate PDF: ${error.message}`);\n      setAdditionPdfLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 660,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Container, {\n        fluid: true,\n        style: {\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\n          transition: \"all 0.3s ease\",\n          padding: \"20px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center my-5\",\n          children: [/*#__PURE__*/_jsxDEV(Spinner, {\n            animation: \"border\",\n            role: \"status\",\n            variant: \"primary\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"visually-hidden\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2\",\n            children: \"Loading fabric details...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  if (!fabricDetail) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Container, {\n        fluid: true,\n        style: {\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\n          transition: \"all 0.3s ease\",\n          padding: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          className: \"text-center\",\n          children: message || \"Error loading fabric details.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 692,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-3\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => navigate(-1),\n            children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 15\n            }, this), \" Back to Fabrics\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 695,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 707,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      style: {\n        marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\n        width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\n        transition: \"all 0.3s ease\",\n        padding: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-secondary\",\n          onClick: () => navigate(-1),\n          className: \"me-2\",\n          children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 13\n          }, this), \" Back\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 717,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mb-0 text-center flex-grow-1\",\n          children: [/*#__PURE__*/_jsxDEV(FaTshirt, {\n            className: \"me-2 text-primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this), fabricDetail ? fabricDetail.fabric_name : 'Loading...']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-primary\",\n            onClick: openPdfModal,\n            className: \"me-2\",\n            children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 15\n            }, this), \" Real-time Inventory\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-success\",\n            onClick: openAdditionPdfModal,\n            className: \"me-2\",\n            children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 15\n            }, this), \" Fabric Addition\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 716,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"text-center\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 746,\n        columnNumber: 21\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"text-center\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 747,\n        columnNumber: 19\n      }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"success\",\n        className: \"text-center\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 748,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"shadow-sm h-100\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              className: \"bg-primary text-white\",\n              children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 19\n                }, this), \"Fabric Information\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(ListGroup, {\n                variant: \"flush\",\n                children: [/*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(FaUserTie, {\n                      className: \"me-2 text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 763,\n                      columnNumber: 23\n                    }, this), \"Suppliers\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-end\",\n                    children: fabricDetail.supplier_names && fabricDetail.supplier_names.length > 0 ? fabricDetail.supplier_names.map((name, index) => /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"info\",\n                      pill: true,\n                      className: \"me-1 mb-1\",\n                      children: name\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 769,\n                      columnNumber: 29\n                    }, this)) : /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"secondary\",\n                      pill: true,\n                      children: \"No suppliers\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 771,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 766,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                      className: \"me-2 text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 777,\n                      columnNumber: 23\n                    }, this), \"Date Added\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 776,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: formatDate(fabricDetail.date_added)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 780,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(FaPalette, {\n                      className: \"me-2 text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 784,\n                      columnNumber: 23\n                    }, this), \"Color Variants\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: \"primary\",\n                    pill: true,\n                    children: fabricDetail.variants ? fabricDetail.variants.length : 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(FaRulerHorizontal, {\n                      className: \"me-2 text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 791,\n                      columnNumber: 23\n                    }, this), \"Total Yards\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 790,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [calculateTotalYards(), \" yards\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 789,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(FaRulerHorizontal, {\n                      className: \"me-2 text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 798,\n                      columnNumber: 23\n                    }, this), \"Available Yards\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [(fabricDetail.variants || []).reduce((total, variant) => total + parseFloat(variant.available_yard), 0).toFixed(2), \" yards\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(FaMoneyBillWave, {\n                      className: \"me-2 text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 805,\n                      columnNumber: 23\n                    }, this), \"Total Value (All)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success fw-bold\",\n                    children: [\"Rs. \", calculateTotalValue()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 808,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(FaMoneyBillWave, {\n                      className: \"me-2 text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 812,\n                      columnNumber: 23\n                    }, this), \"Current Value\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success fw-bold\",\n                    children: [\"Rs. \", calculateCurrentValue()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 815,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 751,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 8,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              className: \"bg-primary text-white\",\n              children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(FaPalette, {\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 19\n                }, this), \"Color Variants\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                hover: true,\n                bordered: true,\n                responsive: true,\n                className: \"align-middle\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"bg-light\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      style: {\n                        width: '15%'\n                      },\n                      children: \"Color\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 834,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      style: {\n                        width: '30%'\n                      },\n                      className: \"text-center\",\n                      children: \"Yard Information\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 835,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      style: {\n                        width: '20%'\n                      },\n                      className: \"text-center\",\n                      children: \"Price per Yard\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 836,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      style: {\n                        width: '20%'\n                      },\n                      className: \"text-center\",\n                      children: \"Current Value\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 837,\n                      columnNumber: 23\n                    }, this), isInventoryManager && /*#__PURE__*/_jsxDEV(\"th\", {\n                      style: {\n                        width: '15%'\n                      },\n                      className: \"text-center\",\n                      children: \"Actions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 839,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: (fabricDetail.variants || []).map(variant => /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '30px',\n                            height: '30px',\n                            backgroundColor: variant.color,\n                            borderRadius: '4px',\n                            border: '1px solid #dee2e6',\n                            marginRight: '10px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 848,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: variant.color\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 858,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 847,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 846,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Total:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 863,\n                          columnNumber: 29\n                        }, this), \" \", variant.total_yard, \" yards\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 862,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Current Stock:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 866,\n                          columnNumber: 29\n                        }, this), ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: variant.available_yard < 10 ? 'text-danger fw-bold' : '',\n                          children: variant.available_yard !== null ? `${variant.available_yard} yards` : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 867,\n                          columnNumber: 29\n                        }, this), variant.available_yard !== null && variant.total_yard > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-1\",\n                          children: /*#__PURE__*/_jsxDEV(ProgressBar, {\n                            now: Math.min(100, variant.available_yard / variant.total_yard * 100),\n                            variant: variant.available_yard < 0.1 * variant.total_yard ? 'danger' : variant.available_yard < 0.3 * variant.total_yard ? 'warning' : 'success',\n                            style: {\n                              height: '8px'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 872,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 871,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 865,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(Button, {\n                          size: \"sm\",\n                          variant: \"outline-info\",\n                          onClick: () => navigate(`/fabric-inventory/${variant.id}`),\n                          children: \"View Inventory\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 884,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 883,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 861,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"text-center\",\n                      children: [\"Rs. \", variant.price_per_yard, \"/yard\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 893,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"text-center fw-bold\",\n                      children: [\"Rs. \", (variant.available_yard * variant.price_per_yard).toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 894,\n                      columnNumber: 25\n                    }, this), isInventoryManager && /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"text-center\",\n                      children: /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"danger\",\n                        size: \"sm\",\n                        onClick: () => handleDeleteClick(variant),\n                        disabled: !fabricDetail.variants || fabricDetail.variants.length <= 1,\n                        title: !fabricDetail.variants || fabricDetail.variants.length <= 1 ? \"Cannot delete the last variant\" : \"Delete variant\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTrash, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 906,\n                          columnNumber: 31\n                        }, this), \" Delete\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 899,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 27\n                    }, this)]\n                  }, variant.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 845,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tfoot\", {\n                  className: \"bg-light\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      colSpan: \"2\",\n                      className: \"text-end fw-bold\",\n                      children: \"Total:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 915,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"text-center\",\n                      children: [fabricDetail.variants.length, \" variants\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 916,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"text-center fw-bold text-success\",\n                      colSpan: isInventoryManager ? 2 : 1,\n                      children: [\"Rs. \", calculateCurrentValue()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 917,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 913,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 750,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showDeleteModal,\n      onHide: closeDeleteModal,\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Confirm Deletion\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 931,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: variantToDelete && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Are you sure you want to delete this fabric variant?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            bordered: true,\n            size: \"sm\",\n            className: \"mt-3\",\n            children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Color:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 941,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: '20px',\n                        height: '20px',\n                        backgroundColor: variantToDelete.color,\n                        borderRadius: '4px',\n                        border: '1px solid #dee2e6',\n                        marginRight: '10px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 944,\n                      columnNumber: 25\n                    }, this), variantToDelete.color_name || variantToDelete.color]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 943,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 942,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 940,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Total Yard:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 959,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [variantToDelete.total_yard, \" yards\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Available Yard:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 963,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [variantToDelete.available_yard, \" yards\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 964,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 962,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Price per Yard:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 967,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 967,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [\"Rs. \", variantToDelete.price_per_yard, \"/yard\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 968,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 966,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 939,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 938,\n            columnNumber: 15\n          }, this), hasDependencies && /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"warning\",\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Warning:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 19\n            }, this), \" This fabric variant cannot be deleted because it is being used in cutting records.\", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"To delete this variant, you must first delete all cutting records that use it. You can view these records by clicking \\\"View Inventory\\\" and checking the cutting history.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 974,\n            columnNumber: 17\n          }, this), !hasDependencies && fabricDetail && fabricDetail.variants && fabricDetail.variants.length > 1 && /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"success\",\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Good news!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 19\n            }, this), \" This fabric variant has no cutting records and can be safely deleted.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 17\n          }, this), fabricDetail && (!fabricDetail.variants || fabricDetail.variants.length <= 1) && /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"warning\",\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Warning:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 19\n            }, this), \" Cannot delete the last variant of a fabric. If you want to remove this fabric completely, please delete the entire fabric definition from the fabric list.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 991,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 934,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: closeDeleteModal,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 999,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"danger\",\n          onClick: confirmDelete,\n          disabled: isDeleting || hasDependencies || fabricDetail && fabricDetail.variants && fabricDetail.variants.length <= 1,\n          children: isDeleting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              as: \"span\",\n              animation: \"border\",\n              size: \"sm\",\n              role: \"status\",\n              \"aria-hidden\": \"true\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1009,\n              columnNumber: 17\n            }, this), \"Deleting...\"]\n          }, void 0, true) : 'Delete Variant'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1002,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 998,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 930,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPdfModal,\n      onHide: () => setShowPdfModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n            className: \"me-2 text-primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1023,\n            columnNumber: 13\n          }, this), \"Export Fabric Variants to PDF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1022,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1021,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted mb-3\",\n          children: [\"This will generate a PDF report containing all color variants of \", fabricDetail === null || fabricDetail === void 0 ? void 0 : fabricDetail.fabric_name, \". The PDF will include fabric name, color, total yard, available yard, price per yard, and total value.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1028,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"info\",\n          children: [/*#__PURE__*/_jsxDEV(FaTable, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1034,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Note:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1035,\n            columnNumber: 13\n          }, this), \" The PDF report will include all color variants for this fabric with their respective inventory details and color swatches.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1033,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1027,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowPdfModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1039,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: generatePDF,\n          disabled: pdfLoading,\n          children: pdfLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              as: \"span\",\n              animation: \"border\",\n              size: \"sm\",\n              role: \"status\",\n              \"aria-hidden\": \"true\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 17\n            }, this), \"Generating PDF...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FaFileDownload, {\n              className: \"me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1061,\n              columnNumber: 17\n            }, this), \" Download PDF\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1042,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1038,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1020,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showAdditionPdfModal,\n      onHide: () => setShowAdditionPdfModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n            className: \"me-2 text-success\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 13\n          }, this), \"Fabric Addition Report\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1071,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1070,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted mb-3\",\n          children: [\"This will generate a PDF report showing the initial fabric addition details for \", fabricDetail === null || fabricDetail === void 0 ? void 0 : fabricDetail.fabric_name, \". The report will include fabric name, supplier, date added, and details of all color variants that were added.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1077,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"info\",\n          children: [/*#__PURE__*/_jsxDEV(FaTable, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1083,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Note:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1084,\n            columnNumber: 13\n          }, this), \" This report focuses on the initial fabric addition data rather than current inventory levels.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1082,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1076,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowAdditionPdfModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1088,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"success\",\n          onClick: generateAdditionPDF,\n          disabled: additionPdfLoading,\n          children: additionPdfLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              as: \"span\",\n              animation: \"border\",\n              size: \"sm\",\n              role: \"status\",\n              \"aria-hidden\": \"true\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1098,\n              columnNumber: 17\n            }, this), \"Generating PDF...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FaFileDownload, {\n              className: \"me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 17\n            }, this), \" Download Report\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1091,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1087,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1069,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ViewFabricVariants, \"4gX3R1H4/lGHgZAGMxtH0HXZ9IY=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ViewFabricVariants;\nexport default ViewFabricVariants;\nvar _c;\n$RefreshReg$(_c, \"ViewFabricVariants\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useParams", "useNavigate", "RoleBasedNavBar", "getUserRole", "hasRole", "Container", "Row", "Col", "Card", "Table", "<PERSON><PERSON>", "Badge", "<PERSON><PERSON>", "Spinner", "ListGroup", "ProgressBar", "Modal", "Form", "FaTshirt", "FaArrowLeft", "FaPalette", "FaRulerHorizontal", "FaMoneyBillWave", "FaUserTie", "FaCalendarAlt", "FaInfoCircle", "FaTrash", "FaEdit", "FaFilePdf", "FaFileDownload", "FaTable", "jsPDF", "autoTable", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ViewFabricVariants", "_s", "id", "fabricDetail", "setFabricDetail", "message", "setMessage", "error", "setError", "success", "setSuccess", "loading", "setLoading", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "isInventoryManager", "setIsInventoryManager", "navigate", "showDeleteModal", "setShowDeleteModal", "variantToDelete", "setVariantToDelete", "isDeleting", "setIsDeleting", "hasDependencies", "setHasDependencies", "showPdfModal", "setShowPdfModal", "pdfLoading", "setPdfLoading", "showAdditionPdfModal", "setShowAdditionPdfModal", "additionPdfLoading", "setAdditionPdfLoading", "handleResize", "addEventListener", "removeEventListener", "get", "then", "response", "data", "catch", "console", "formatDate", "dateString", "options", "year", "month", "day", "Date", "toLocaleDateString", "undefined", "calculateTotalValue", "variants", "reduce", "total", "variant", "total_yard", "price_per_yard", "toFixed", "calculateCurrentValue", "available_yard", "calculateTotalYards", "parseFloat", "checkVariantDependencies", "variantId", "log", "hasReferences", "record", "details", "length", "usesVariant", "some", "detail", "fabric_variant", "historyResponse", "cutting_history", "historyError", "status", "handleDeleteClick", "hasDeps", "confirmDelete", "delete", "updatedVariants", "filter", "v", "color_name", "color", "setTimeout", "deleteError", "put", "fabric_definition", "alternativeError", "_error$response", "_error$response$data", "errorMessage", "closeDeleteModal", "openPdfModal", "openAdditionPdfModal", "generatePDF", "doc", "orientation", "unit", "format", "titleFontSize", "headingFontSize", "normalFontSize", "smallFontSize", "baseUrl", "location", "origin", "addImage", "logoError", "warn", "setFillColor", "rect", "setFontSize", "setTextColor", "text", "align", "setFont", "fabricInfo", "fabric_name", "supplier_names", "join", "date_added", "toString", "startY", "head", "body", "theme", "headStyles", "fillColor", "textColor", "styles", "fontSize", "lastAutoTable", "finalY", "variantsData", "map", "currentValue", "columnStyles", "cellWidth", "didDrawCell", "section", "column", "index", "rowIndex", "row", "colorCode", "x", "y", "width", "height", "cell", "padding", "swatchX", "swatchY", "swatchWidth", "swatchHeight", "setDrawColor", "toLocaleString", "cleanFabricName", "replace", "save", "toISOString", "slice", "generateAdditionPDF", "supplier_name", "totalYards", "totalValue", "halign", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fluid", "style", "marginLeft", "transition", "className", "animation", "role", "onClick", "lg", "md", "Header", "Body", "<PERSON><PERSON>", "name", "bg", "pill", "hover", "bordered", "responsive", "backgroundColor", "borderRadius", "border", "marginRight", "now", "Math", "min", "size", "disabled", "title", "colSpan", "show", "onHide", "centered", "closeButton", "Title", "Footer", "as", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/GitHub/Pri_Fashion_/frontend/src/pages/ViewFabricVariants.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { useParams, useNavigate } from \"react-router-dom\";\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport { getUserRole, hasRole } from '../utils/auth';\r\nimport {\r\n  Container, Row, Col, Card, Table, Button,\r\n  Badge, Alert, Spinner, ListGroup, ProgressBar, Modal,\r\n  Form\r\n} from 'react-bootstrap';\r\nimport {\r\n  FaTshirt, FaArrowLeft, FaPalette,\r\n  FaRulerHorizontal, FaMoneyBillWave, FaUserTie,\r\n  FaCalendarAlt, FaInfoCircle, FaTrash, FaEdit,\r\n  FaFilePdf, FaFileDownload, FaTable\r\n} from 'react-icons/fa';\r\nimport { jsPDF } from \"jspdf\";\r\nimport autoTable from 'jspdf-autotable';\r\n\r\nconst ViewFabricVariants = () => {\r\n  const { id } = useParams(); // FabricDefinition ID from URL\r\n  const [fabricDetail, setFabricDetail] = useState(null);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [error, setError] = useState(\"\");\r\n  const [success, setSuccess] = useState(\"\");\r\n  const [loading, setLoading] = useState(true);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [isInventoryManager, setIsInventoryManager] = useState(hasRole('Inventory Manager'));\r\n  const navigate = useNavigate();\r\n\r\n  // Delete confirmation modal states\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [variantToDelete, setVariantToDelete] = useState(null);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [hasDependencies, setHasDependencies] = useState(false);\r\n\r\n  // PDF export states\r\n  const [showPdfModal, setShowPdfModal] = useState(false);\r\n  const [pdfLoading, setPdfLoading] = useState(false);\r\n  const [showAdditionPdfModal, setShowAdditionPdfModal] = useState(false);\r\n  const [additionPdfLoading, setAdditionPdfLoading] = useState(false);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    axios\r\n      .get(`http://localhost:8000/api/fabric-definitions/${id}/`)\r\n      .then((response) => {\r\n        setFabricDetail(response.data);\r\n        setLoading(false);\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"Error fetching fabric definition:\", error);\r\n        setMessage(\"Error loading fabric details.\");\r\n        setLoading(false);\r\n      });\r\n  }, [id]);\r\n\r\n  // Format date for display\r\n  const formatDate = (dateString) => {\r\n    const options = { year: 'numeric', month: 'short', day: 'numeric' };\r\n    return new Date(dateString).toLocaleDateString(undefined, options);\r\n  };\r\n\r\n  // Calculate total inventory value\r\n  const calculateTotalValue = () => {\r\n    if (!fabricDetail || !fabricDetail.variants) return 0;\r\n    return fabricDetail.variants.reduce((total, variant) => {\r\n      return total + (variant.total_yard * variant.price_per_yard);\r\n    }, 0).toFixed(2);\r\n  };\r\n\r\n  // Calculate current inventory value (based on available yards)\r\n  const calculateCurrentValue = () => {\r\n    if (!fabricDetail || !fabricDetail.variants) return 0;\r\n    return fabricDetail.variants.reduce((total, variant) => {\r\n      return total + (variant.available_yard * variant.price_per_yard);\r\n    }, 0).toFixed(2);\r\n  };\r\n\r\n  // Calculate total yards\r\n  const calculateTotalYards = () => {\r\n    if (!fabricDetail || !fabricDetail.variants) return 0;\r\n    return fabricDetail.variants.reduce((total, variant) => {\r\n      return total + parseFloat(variant.total_yard);\r\n    }, 0).toFixed(2);\r\n  };\r\n\r\n  // Check if variant has any cutting records associated with it\r\n  const checkVariantDependencies = async (variantId) => {\r\n    try {\r\n      // Direct check for cutting records that use this fabric variant\r\n      // This is more reliable than using the history endpoint\r\n      const response = await axios.get(`http://localhost:8000/api/cutting/cutting-records/`);\r\n      console.log(\"All cutting records:\", response.data);\r\n\r\n      // Check if any cutting record details reference this variant\r\n      let hasReferences = false;\r\n\r\n      for (const record of response.data) {\r\n        if (record.details && record.details.length > 0) {\r\n          // Check if any detail uses this variant\r\n          const usesVariant = record.details.some(detail => detail.fabric_variant === variantId);\r\n\r\n          if (usesVariant) {\r\n            console.log(`Variant ${variantId} is used in cutting record ${record.id}`);\r\n            hasReferences = true;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      if (hasReferences) {\r\n        return true;\r\n      }\r\n\r\n      // Fallback to the history endpoint if needed\r\n      try {\r\n        const historyResponse = await axios.get(`http://localhost:8000/api/cutting/fabric-variant/${variantId}/history/`);\r\n        console.log(\"Variant dependency check response:\", historyResponse.data);\r\n\r\n        // If there are cutting records in the history, the variant has dependencies\r\n        if (historyResponse.data &&\r\n            historyResponse.data.cutting_history &&\r\n            historyResponse.data.cutting_history.length > 0) {\r\n          console.log(`Variant ${variantId} has ${historyResponse.data.cutting_history.length} cutting records`);\r\n          return true;\r\n        }\r\n      } catch (historyError) {\r\n        console.error(\"Error checking variant history:\", historyError);\r\n        // Continue with the main function even if history check fails\r\n      }\r\n\r\n      // No cutting records found - variant can be safely deleted\r\n      console.log(`Variant ${variantId} has no cutting records`);\r\n      return false;\r\n    } catch (error) {\r\n      console.error(\"Error checking variant dependencies:\", error);\r\n\r\n      // If it's a 404 error, it means the endpoint doesn't exist or the variant doesn't exist\r\n      // In this case, we can assume there are no dependencies\r\n      if (error.response && error.response.status === 404) {\r\n        console.log(`Variant ${variantId} not found or endpoint not available`);\r\n        return false;\r\n      }\r\n\r\n      // For other errors, log more details to help with debugging\r\n      console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\r\n\r\n      // If there's an error, assume there might be dependencies to prevent accidental deletion\r\n      return true;\r\n    }\r\n  };\r\n\r\n  // Handle delete button click\r\n  const handleDeleteClick = async (variant) => {\r\n    setVariantToDelete(variant);\r\n    setIsDeleting(false);\r\n    setHasDependencies(false); // Default to no dependencies\r\n\r\n    try {\r\n      // Check if this is the last variant\r\n      if (!fabricDetail.variants || fabricDetail.variants.length <= 1) {\r\n        setError(\"Cannot delete the last variant of a fabric. Please delete the entire fabric definition instead.\");\r\n        return;\r\n      }\r\n\r\n      // Check for dependencies\r\n      const hasDeps = await checkVariantDependencies(variant.id);\r\n      setHasDependencies(hasDeps);\r\n\r\n      // Show the confirmation modal\r\n      setShowDeleteModal(true);\r\n    } catch (error) {\r\n      console.error(\"Error in handleDeleteClick:\", error);\r\n      setError(\"An error occurred while checking if this variant can be deleted. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // Handle delete confirmation\r\n  const confirmDelete = async () => {\r\n    if (hasDependencies) {\r\n      return; // Don't allow deletion if there are dependencies\r\n    }\r\n\r\n    setIsDeleting(true);\r\n\r\n    try {\r\n      console.log(`Attempting to delete variant with ID: ${variantToDelete.id}`);\r\n\r\n      // Double-check if this is the last variant\r\n      if (!fabricDetail.variants || fabricDetail.variants.length <= 1) {\r\n        setError(\"Cannot delete the last variant of a fabric. Please delete the entire fabric definition instead.\");\r\n        setIsDeleting(false);\r\n        return;\r\n      }\r\n\r\n      // Force a direct delete without checking dependencies again\r\n      try {\r\n        const response = await axios.delete(`http://localhost:8000/api/fabric-variants/${variantToDelete.id}/`);\r\n        console.log(\"Delete response:\", response);\r\n\r\n        // Update the fabric detail by removing the deleted variant\r\n        const updatedVariants = fabricDetail.variants.filter(v => v.id !== variantToDelete.id);\r\n        setFabricDetail({\r\n          ...fabricDetail,\r\n          variants: updatedVariants\r\n        });\r\n\r\n        // Close the modal and show success message\r\n        setShowDeleteModal(false);\r\n        setSuccess(`Fabric variant \"${variantToDelete.color_name || variantToDelete.color}\" deleted successfully!`);\r\n\r\n        // Clear success message after 3 seconds\r\n        setTimeout(() => {\r\n          setSuccess(\"\");\r\n        }, 3000);\r\n      } catch (deleteError) {\r\n        console.error(\"Error in direct delete attempt:\", deleteError);\r\n\r\n        // If the direct delete fails, try a workaround for variants with no cutting records\r\n        if (deleteError.response && (deleteError.response.status === 400 || deleteError.response.status === 500)) {\r\n          console.log(\"Attempting alternative delete method...\");\r\n\r\n          try {\r\n            // Update the variant with minimal data before deleting\r\n            await axios.put(`http://localhost:8000/api/fabric-variants/${variantToDelete.id}/`, {\r\n              fabric_definition: variantToDelete.fabric_definition,\r\n              color: variantToDelete.color,\r\n              color_name: variantToDelete.color_name || variantToDelete.color,\r\n              total_yard: 0,\r\n              available_yard: 0,\r\n              price_per_yard: 0\r\n            });\r\n\r\n            // Try deleting again\r\n            await axios.delete(`http://localhost:8000/api/fabric-variants/${variantToDelete.id}/`);\r\n\r\n            // Update the fabric detail by removing the deleted variant\r\n            const updatedVariants = fabricDetail.variants.filter(v => v.id !== variantToDelete.id);\r\n            setFabricDetail({\r\n              ...fabricDetail,\r\n              variants: updatedVariants\r\n            });\r\n\r\n            // Close the modal and show success message\r\n            setShowDeleteModal(false);\r\n            setSuccess(`Fabric variant \"${variantToDelete.color_name || variantToDelete.color}\" deleted successfully!`);\r\n\r\n            // Clear success message after 3 seconds\r\n            setTimeout(() => {\r\n              setSuccess(\"\");\r\n            }, 3000);\r\n          } catch (alternativeError) {\r\n            console.error(\"Alternative delete method failed:\", alternativeError);\r\n            throw alternativeError; // Re-throw to be caught by the outer catch block\r\n          }\r\n        } else {\r\n          throw deleteError; // Re-throw to be caught by the outer catch block\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting fabric variant:\", error);\r\n\r\n      // Log detailed error information\r\n      console.error(\"Error details:\", error.response ? error.response.data : \"No response data\");\r\n      console.error(\"Error status:\", error.response ? error.response.status : \"No status\");\r\n\r\n      // Provide more specific error messages based on the error\r\n      let errorMessage = \"Failed to delete fabric variant. Please try again.\";\r\n\r\n      if (error.response) {\r\n        if (error.response.status === 400) {\r\n          errorMessage = error.response.data.detail || \"Bad request. The variant may be referenced by other records.\";\r\n        } else if (error.response.status === 403) {\r\n          errorMessage = \"You don't have permission to delete this variant.\";\r\n        } else if (error.response.status === 404) {\r\n          errorMessage = \"Variant not found. It may have been already deleted.\";\r\n        } else if (error.response.status === 500) {\r\n          errorMessage = \"Server error. Please contact the administrator.\";\r\n        }\r\n      }\r\n\r\n      setError(error.response?.data?.message || errorMessage);\r\n\r\n      // Clear error after 5 seconds\r\n      setTimeout(() => {\r\n        setError(\"\");\r\n      }, 5000);\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  // Close the delete modal\r\n  const closeDeleteModal = () => {\r\n    setShowDeleteModal(false);\r\n    setVariantToDelete(null);\r\n    setHasDependencies(false);\r\n  };\r\n\r\n  // Open Real-time Inventory PDF export modal\r\n  const openPdfModal = () => {\r\n    setShowPdfModal(true);\r\n  };\r\n\r\n  // Open Fabric Addition Report PDF modal\r\n  const openAdditionPdfModal = () => {\r\n    setShowAdditionPdfModal(true);\r\n  };\r\n\r\n  // Generate and download Real-time Inventory PDF file\r\n  const generatePDF = () => {\r\n    setPdfLoading(true);\r\n\r\n    if (!fabricDetail || !fabricDetail.variants) {\r\n      setError(\"No fabric data available to export\");\r\n      setPdfLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Create a new jsPDF instance\r\n      const doc = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Set font sizes and styles\r\n      const titleFontSize = 18;\r\n      const headingFontSize = 14;\r\n      const normalFontSize = 10;\r\n      const smallFontSize = 8;\r\n\r\n      // Add logo\r\n      try {\r\n        // Get the base URL for the current environment\r\n        const baseUrl = window.location.origin;\r\n\r\n        // Add the logo to the PDF\r\n        doc.addImage(`${baseUrl}/logo.png`, 'PNG', 14, 10, 20, 20);\r\n      } catch (logoError) {\r\n        console.warn(\"Could not add logo to PDF:\", logoError);\r\n\r\n        // Fallback to a simple placeholder if the logo can't be loaded\r\n        doc.setFillColor(41, 128, 185); // Primary blue color\r\n        doc.rect(14, 10, 20, 20, 'F');\r\n\r\n        // Add \"PF\" text as a simple logo\r\n        doc.setFontSize(14);\r\n        doc.setTextColor(255, 255, 255);\r\n        doc.text(\"PF\", 24, 22, { align: 'center' });\r\n        doc.setTextColor(0, 0, 0); // Reset text color to black\r\n      }\r\n\r\n      // Add title\r\n      doc.setFontSize(titleFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Real-time Fabric Inventory Report', 105, 20, { align: 'center' });\r\n\r\n      // Add fabric information section\r\n      doc.setFontSize(headingFontSize);\r\n      doc.text('Fabric Information', 20, 35);\r\n\r\n      doc.setFontSize(normalFontSize);\r\n      doc.setFont('helvetica', 'normal');\r\n\r\n      // Add fabric details\r\n      const fabricInfo = [\r\n        ['Fabric Name', fabricDetail.fabric_name || 'N/A'],\r\n        ['Suppliers', fabricDetail.supplier_names && fabricDetail.supplier_names.length > 0\r\n          ? fabricDetail.supplier_names.join(', ')\r\n          : 'No suppliers'],\r\n        ['Date Added', formatDate(fabricDetail.date_added) || 'N/A'],\r\n        ['Total Variants', (fabricDetail.variants ? fabricDetail.variants.length : 0).toString()],\r\n        ['Total Yards', calculateTotalYards().toString()],\r\n        ['Available Yards', (fabricDetail.variants || []).reduce((total, variant) => total + parseFloat(variant.available_yard), 0).toFixed(2)],\r\n        ['Total Value (All)', `Rs. ${calculateTotalValue()}`],\r\n        ['Current Value', `Rs. ${calculateCurrentValue()}`]\r\n      ];\r\n\r\n      // Add fabric info table\r\n      autoTable(doc, {\r\n        startY: 40,\r\n        head: [['Property', 'Value']],\r\n        body: fabricInfo,\r\n        theme: 'grid',\r\n        headStyles: { fillColor: [41, 128, 185], textColor: 255 },\r\n        styles: { fontSize: 10 }\r\n      });\r\n\r\n      // Add variants section\r\n      doc.setFontSize(headingFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Color Variants', 20, doc.lastAutoTable.finalY + 15);\r\n\r\n      // Prepare data for variants table\r\n      const variantsData = (fabricDetail.variants || []).map(variant => {\r\n        // Calculate current value based on available yard * price per yard\r\n        const currentValue = (variant.available_yard * variant.price_per_yard).toFixed(2);\r\n        return [\r\n          variant.color_name || variant.color || 'N/A',\r\n          '', // Empty cell for color display\r\n          variant.color || 'N/A', // Color code in separate column\r\n          variant.total_yard.toString(),\r\n          variant.available_yard.toString(),\r\n          `Rs. ${variant.price_per_yard}`,\r\n          `Rs. ${currentValue}`\r\n        ];\r\n      });\r\n\r\n      // No need to define dimensions here as we'll use the cell dimensions\r\n\r\n      // Add variants table\r\n      autoTable(doc, {\r\n        startY: doc.lastAutoTable.finalY + 20,\r\n        head: [['Color Name', 'Color', 'Color Code', 'Total Yard', 'Available Yard', 'Price/Yard', 'Current Value']],\r\n        body: variantsData,\r\n        theme: 'striped',\r\n        styles: { fontSize: 10 },\r\n        headStyles: { fillColor: [41, 128, 185], textColor: 255 },\r\n        columnStyles: {\r\n          1: { cellWidth: 30 }, // Wider column for color swatch\r\n          2: { cellWidth: 30 }  // Wider column for color code\r\n        },\r\n        didDrawCell: (data) => {\r\n          // Add color box in the color display column (index 1)\r\n          if (data.section === 'body' && data.column.index === 1) {\r\n            const rowIndex = data.row.index;\r\n            const colorCode = variantsData[rowIndex][2]; // Get color code from the next column\r\n\r\n            // Only draw if it's a valid color code\r\n            if (colorCode && colorCode !== 'N/A') {\r\n              const { x, y, width, height } = data.cell;\r\n\r\n              // Create a color swatch that fits within the cell with some padding\r\n              const padding = 2;\r\n              const swatchX = x + padding;\r\n              const swatchY = y + padding;\r\n              const swatchWidth = width - (padding * 2);\r\n              const swatchHeight = height - (padding * 2);\r\n\r\n              // Draw the color swatch\r\n              doc.setFillColor(colorCode);\r\n              doc.rect(swatchX, swatchY, swatchWidth, swatchHeight, 'F');\r\n\r\n              // Add border around the color box\r\n              doc.setDrawColor(100, 100, 100); // Darker border for better visibility\r\n              doc.rect(swatchX, swatchY, swatchWidth, swatchHeight, 'S');\r\n            }\r\n          }\r\n        }\r\n      });\r\n\r\n      // Add footer\r\n      doc.setFontSize(smallFontSize);\r\n      doc.setFont('helvetica', 'italic');\r\n      doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, { align: 'center' });\r\n      doc.text('Pri Fashion Garment Management System', 105, 285, { align: 'center' });\r\n\r\n      // Save the PDF\r\n      const cleanFabricName = fabricDetail.fabric_name.replace(/[^a-zA-Z0-9]/g, '_');\r\n      doc.save(`Fabric_Inventory_${cleanFabricName}_${new Date().toISOString().slice(0, 10)}.pdf`);\r\n\r\n      setPdfLoading(false);\r\n      setShowPdfModal(false);\r\n    } catch (error) {\r\n      console.error(\"Error generating PDF:\", error);\r\n      setError(`Failed to generate PDF: ${error.message}`);\r\n      setPdfLoading(false);\r\n    }\r\n  };\r\n\r\n  // Generate and download Fabric Addition Report PDF\r\n  const generateAdditionPDF = () => {\r\n    setAdditionPdfLoading(true);\r\n\r\n    if (!fabricDetail || !fabricDetail.variants) {\r\n      setError(\"No fabric data available to export\");\r\n      setAdditionPdfLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Create a new jsPDF instance\r\n      const doc = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Set font sizes and styles\r\n      const titleFontSize = 18;\r\n      const headingFontSize = 14;\r\n      const normalFontSize = 10;\r\n      const smallFontSize = 8;\r\n\r\n      // Add logo\r\n      try {\r\n        // Get the base URL for the current environment\r\n        const baseUrl = window.location.origin;\r\n\r\n        // Add the logo to the PDF\r\n        doc.addImage(`${baseUrl}/logo.png`, 'PNG', 14, 10, 20, 20);\r\n      } catch (logoError) {\r\n        console.warn(\"Could not add logo to PDF:\", logoError);\r\n\r\n        // Fallback to a simple placeholder if the logo can't be loaded\r\n        doc.setFillColor(41, 128, 185); // Primary blue color\r\n        doc.rect(14, 10, 20, 20, 'F');\r\n\r\n        // Add \"PF\" text as a simple logo\r\n        doc.setFontSize(14);\r\n        doc.setTextColor(255, 255, 255);\r\n        doc.text(\"PF\", 24, 22, { align: 'center' });\r\n        doc.setTextColor(0, 0, 0); // Reset text color to black\r\n      }\r\n\r\n      // Add title\r\n      doc.setFontSize(titleFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Fabric Addition Report', 105, 20, { align: 'center' });\r\n\r\n      // Add fabric information section\r\n      doc.setFontSize(headingFontSize);\r\n      doc.text('Fabric Information', 20, 35);\r\n\r\n      doc.setFontSize(normalFontSize);\r\n      doc.setFont('helvetica', 'normal');\r\n\r\n      // Add fabric details\r\n      const fabricInfo = [\r\n        ['Fabric Name', fabricDetail.fabric_name || 'N/A'],\r\n        ['Supplier', fabricDetail.supplier_name || 'N/A'],\r\n        ['Date Added', formatDate(fabricDetail.date_added) || 'N/A'],\r\n        ['Total Variants', fabricDetail.variants.length.toString()]\r\n      ];\r\n\r\n      // Add fabric info table\r\n      autoTable(doc, {\r\n        startY: 40,\r\n        head: [['Property', 'Value']],\r\n        body: fabricInfo,\r\n        theme: 'grid',\r\n        headStyles: { fillColor: [41, 128, 185], textColor: 255 },\r\n        styles: { fontSize: 10 }\r\n      });\r\n\r\n      // Add variants section\r\n      doc.setFontSize(headingFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Added Fabric Variants', 20, doc.lastAutoTable.finalY + 15);\r\n\r\n      // Prepare data for variants table\r\n      const variantsData = (fabricDetail.variants || []).map(variant => {\r\n        return [\r\n          variant.color_name || variant.color || 'N/A',\r\n          '', // Empty cell for color display\r\n          variant.color || 'N/A', // Color code in separate column\r\n          variant.total_yard.toString(),\r\n          `Rs. ${variant.price_per_yard}`,\r\n          `Rs. ${(variant.total_yard * variant.price_per_yard).toFixed(2)}`\r\n        ];\r\n      });\r\n\r\n      // No need to define dimensions here as we'll use the cell dimensions\r\n\r\n      // Add variants table\r\n      autoTable(doc, {\r\n        startY: doc.lastAutoTable.finalY + 20,\r\n        head: [['Color Name', 'Color', 'Color Code', 'Added Yard', 'Price/Yard', 'Total Value']],\r\n        body: variantsData,\r\n        theme: 'striped',\r\n        styles: { fontSize: 10 },\r\n        headStyles: { fillColor: [41, 128, 185], textColor: 255 },\r\n        columnStyles: {\r\n          1: { cellWidth: 30 }, // Wider column for color swatch\r\n          2: { cellWidth: 30 }  // Wider column for color code\r\n        },\r\n        didDrawCell: (data) => {\r\n          // Add color box in the color display column (index 1)\r\n          if (data.section === 'body' && data.column.index === 1) {\r\n            const rowIndex = data.row.index;\r\n            const colorCode = variantsData[rowIndex][2]; // Get color code from the next column\r\n\r\n            // Only draw if it's a valid color code\r\n            if (colorCode && colorCode !== 'N/A') {\r\n              const { x, y, width, height } = data.cell;\r\n\r\n              // Create a color swatch that fits within the cell with some padding\r\n              const padding = 2;\r\n              const swatchX = x + padding;\r\n              const swatchY = y + padding;\r\n              const swatchWidth = width - (padding * 2);\r\n              const swatchHeight = height - (padding * 2);\r\n\r\n              // Draw the color swatch\r\n              doc.setFillColor(colorCode);\r\n              doc.rect(swatchX, swatchY, swatchWidth, swatchHeight, 'F');\r\n\r\n              // Add border around the color box\r\n              doc.setDrawColor(100, 100, 100); // Darker border for better visibility\r\n              doc.rect(swatchX, swatchY, swatchWidth, swatchHeight, 'S');\r\n            }\r\n          }\r\n        }\r\n      });\r\n\r\n      // Add summary section\r\n      doc.setFontSize(headingFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Summary', 20, doc.lastAutoTable.finalY + 15);\r\n\r\n      // Calculate totals\r\n      const totalYards = calculateTotalYards();\r\n      const totalValue = calculateTotalValue();\r\n\r\n      // Add summary table\r\n      autoTable(doc, {\r\n        startY: doc.lastAutoTable.finalY + 20,\r\n        head: [['Total Yards Added', 'Total Value']],\r\n        body: [[totalYards, `Rs. ${totalValue}`]],\r\n        theme: 'grid',\r\n        styles: { fontSize: 10, halign: 'center' },\r\n        headStyles: { fillColor: [41, 128, 185], textColor: 255 }\r\n      });\r\n\r\n      // Add footer\r\n      doc.setFontSize(smallFontSize);\r\n      doc.setFont('helvetica', 'italic');\r\n      doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, { align: 'center' });\r\n      doc.text('Pri Fashion Garment Management System', 105, 285, { align: 'center' });\r\n\r\n      // Save the PDF\r\n      const cleanFabricName = fabricDetail.fabric_name.replace(/[^a-zA-Z0-9]/g, '_');\r\n      doc.save(`Fabric_Addition_${cleanFabricName}_${new Date().toISOString().slice(0, 10)}.pdf`);\r\n\r\n      setAdditionPdfLoading(false);\r\n      setShowAdditionPdfModal(false);\r\n    } catch (error) {\r\n      console.error(\"Error generating PDF:\", error);\r\n      setError(`Failed to generate PDF: ${error.message}`);\r\n      setAdditionPdfLoading(false);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <>\r\n        <RoleBasedNavBar />\r\n        <Container fluid\r\n          style={{\r\n            marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n            width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n            transition: \"all 0.3s ease\",\r\n            padding: \"20px\"\r\n          }}\r\n        >\r\n          <div className=\"text-center my-5\">\r\n            <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </Spinner>\r\n            <p className=\"mt-2\">Loading fabric details...</p>\r\n          </div>\r\n        </Container>\r\n      </>\r\n    );\r\n  }\r\n\r\n  if (!fabricDetail) {\r\n    return (\r\n      <>\r\n        <RoleBasedNavBar />\r\n        <Container fluid\r\n          style={{\r\n            marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n            width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n            transition: \"all 0.3s ease\",\r\n            padding: \"20px\"\r\n          }}\r\n        >\r\n          <Alert variant=\"danger\" className=\"text-center\">\r\n            {message || \"Error loading fabric details.\"}\r\n          </Alert>\r\n          <div className=\"text-center mt-3\">\r\n            <Button variant=\"secondary\" onClick={() => navigate(-1)}>\r\n              <FaArrowLeft className=\"me-2\" /> Back to Fabrics\r\n            </Button>\r\n          </div>\r\n        </Container>\r\n      </>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <Container fluid\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n          <Button\r\n            variant=\"outline-secondary\"\r\n            onClick={() => navigate(-1)}\r\n            className=\"me-2\"\r\n          >\r\n            <FaArrowLeft className=\"me-2\" /> Back\r\n          </Button>\r\n          <h2 className=\"mb-0 text-center flex-grow-1\">\r\n            <FaTshirt className=\"me-2 text-primary\" />\r\n            {fabricDetail ? fabricDetail.fabric_name : 'Loading...'}\r\n          </h2>\r\n          <div>\r\n            <Button\r\n              variant=\"outline-primary\"\r\n              onClick={openPdfModal}\r\n              className=\"me-2\"\r\n            >\r\n              <FaFilePdf className=\"me-2\" /> Real-time Inventory\r\n            </Button>\r\n            <Button\r\n              variant=\"outline-success\"\r\n              onClick={openAdditionPdfModal}\r\n              className=\"me-2\"\r\n            >\r\n              <FaFilePdf className=\"me-2\" /> Fabric Addition\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        {message && <Alert variant=\"danger\" className=\"text-center\">{message}</Alert>}\r\n        {error && <Alert variant=\"danger\" className=\"text-center\">{error}</Alert>}\r\n        {success && <Alert variant=\"success\" className=\"text-center\">{success}</Alert>}\r\n\r\n        <Row className=\"mb-4\">\r\n          <Col lg={4} md={12}>\r\n            <Card className=\"shadow-sm h-100\">\r\n              <Card.Header className=\"bg-primary text-white\">\r\n                <h5 className=\"mb-0\">\r\n                  <FaInfoCircle className=\"me-2\" />\r\n                  Fabric Information\r\n                </h5>\r\n              </Card.Header>\r\n              <Card.Body>\r\n                <ListGroup variant=\"flush\">\r\n                  <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                    <div>\r\n                      <FaUserTie className=\"me-2 text-secondary\" />\r\n                      Suppliers\r\n                    </div>\r\n                    <div className=\"text-end\">\r\n                      {fabricDetail.supplier_names && fabricDetail.supplier_names.length > 0\r\n                        ? fabricDetail.supplier_names.map((name, index) => (\r\n                            <Badge key={index} bg=\"info\" pill className=\"me-1 mb-1\">{name}</Badge>\r\n                          ))\r\n                        : <Badge bg=\"secondary\" pill>No suppliers</Badge>\r\n                      }\r\n                    </div>\r\n                  </ListGroup.Item>\r\n                  <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                    <div>\r\n                      <FaCalendarAlt className=\"me-2 text-secondary\" />\r\n                      Date Added\r\n                    </div>\r\n                    <span>{formatDate(fabricDetail.date_added)}</span>\r\n                  </ListGroup.Item>\r\n                  <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                    <div>\r\n                      <FaPalette className=\"me-2 text-secondary\" />\r\n                      Color Variants\r\n                    </div>\r\n                    <Badge bg=\"primary\" pill>{fabricDetail.variants ? fabricDetail.variants.length : 0}</Badge>\r\n                  </ListGroup.Item>\r\n                  <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                    <div>\r\n                      <FaRulerHorizontal className=\"me-2 text-secondary\" />\r\n                      Total Yards\r\n                    </div>\r\n                    <span>{calculateTotalYards()} yards</span>\r\n                  </ListGroup.Item>\r\n                  <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                    <div>\r\n                      <FaRulerHorizontal className=\"me-2 text-secondary\" />\r\n                      Available Yards\r\n                    </div>\r\n                    <span>{(fabricDetail.variants || []).reduce((total, variant) => total + parseFloat(variant.available_yard), 0).toFixed(2)} yards</span>\r\n                  </ListGroup.Item>\r\n                  <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                    <div>\r\n                      <FaMoneyBillWave className=\"me-2 text-secondary\" />\r\n                      Total Value (All)\r\n                    </div>\r\n                    <span className=\"text-success fw-bold\">Rs. {calculateTotalValue()}</span>\r\n                  </ListGroup.Item>\r\n                  <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                    <div>\r\n                      <FaMoneyBillWave className=\"me-2 text-secondary\" />\r\n                      Current Value\r\n                    </div>\r\n                    <span className=\"text-success fw-bold\">Rs. {calculateCurrentValue()}</span>\r\n                  </ListGroup.Item>\r\n                </ListGroup>\r\n              </Card.Body>\r\n            </Card>\r\n          </Col>\r\n\r\n          <Col lg={8} md={12}>\r\n            <Card className=\"shadow-sm\">\r\n              <Card.Header className=\"bg-primary text-white\">\r\n                <h5 className=\"mb-0\">\r\n                  <FaPalette className=\"me-2\" />\r\n                  Color Variants\r\n                </h5>\r\n              </Card.Header>\r\n              <Card.Body>\r\n                <Table hover bordered responsive className=\"align-middle\">\r\n                  <thead className=\"bg-light\">\r\n                    <tr>\r\n                      <th style={{ width: '15%' }}>Color</th>\r\n                      <th style={{ width: '30%' }} className=\"text-center\">Yard Information</th>\r\n                      <th style={{ width: '20%' }} className=\"text-center\">Price per Yard</th>\r\n                      <th style={{ width: '20%' }} className=\"text-center\">Current Value</th>\r\n                      {isInventoryManager && (\r\n                        <th style={{ width: '15%' }} className=\"text-center\">Actions</th>\r\n                      )}\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {(fabricDetail.variants || []).map((variant) => (\r\n                      <tr key={variant.id}>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <div\r\n                              style={{\r\n                                width: '30px',\r\n                                height: '30px',\r\n                                backgroundColor: variant.color,\r\n                                borderRadius: '4px',\r\n                                border: '1px solid #dee2e6',\r\n                                marginRight: '10px'\r\n                              }}\r\n                            />\r\n                            <span>{variant.color}</span>\r\n                          </div>\r\n                        </td>\r\n                        <td className=\"text-center\">\r\n                          <div className=\"mb-1\">\r\n                            <strong>Total:</strong> {variant.total_yard} yards\r\n                          </div>\r\n                          <div className=\"mb-2\">\r\n                            <strong>Current Stock:</strong>{' '}\r\n                            <span className={variant.available_yard < 10 ? 'text-danger fw-bold' : ''}>\r\n                              {variant.available_yard !== null ? `${variant.available_yard} yards` : 'N/A'}\r\n                            </span>\r\n                            {variant.available_yard !== null && variant.total_yard > 0 && (\r\n                              <div className=\"mt-1\">\r\n                                <ProgressBar\r\n                                  now={Math.min(100, (variant.available_yard / variant.total_yard) * 100)}\r\n                                  variant={\r\n                                    variant.available_yard < 0.1 * variant.total_yard ? 'danger' :\r\n                                    variant.available_yard < 0.3 * variant.total_yard ? 'warning' : 'success'\r\n                                  }\r\n                                  style={{ height: '8px' }}\r\n                                />\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                          <div>\r\n                            <Button\r\n                              size=\"sm\"\r\n                              variant=\"outline-info\"\r\n                              onClick={() => navigate(`/fabric-inventory/${variant.id}`)}\r\n                            >\r\n                              View Inventory\r\n                            </Button>\r\n                          </div>\r\n                        </td>\r\n                        <td className=\"text-center\">Rs. {variant.price_per_yard}/yard</td>\r\n                        <td className=\"text-center fw-bold\">\r\n                          Rs. {(variant.available_yard * variant.price_per_yard).toFixed(2)}\r\n                        </td>\r\n                        {isInventoryManager && (\r\n                          <td className=\"text-center\">\r\n                            <Button\r\n                              variant=\"danger\"\r\n                              size=\"sm\"\r\n                              onClick={() => handleDeleteClick(variant)}\r\n                              disabled={!fabricDetail.variants || fabricDetail.variants.length <= 1}\r\n                              title={!fabricDetail.variants || fabricDetail.variants.length <= 1 ? \"Cannot delete the last variant\" : \"Delete variant\"}\r\n                            >\r\n                              <FaTrash className=\"me-1\" /> Delete\r\n                            </Button>\r\n                          </td>\r\n                        )}\r\n                      </tr>\r\n                    ))}\r\n                  </tbody>\r\n                  <tfoot className=\"bg-light\">\r\n                    <tr>\r\n                      <td colSpan=\"2\" className=\"text-end fw-bold\">Total:</td>\r\n                      <td className=\"text-center\">{fabricDetail.variants.length} variants</td>\r\n                      <td className=\"text-center fw-bold text-success\" colSpan={isInventoryManager ? 2 : 1}>\r\n                        Rs. {calculateCurrentValue()}\r\n                      </td>\r\n                    </tr>\r\n                  </tfoot>\r\n                </Table>\r\n              </Card.Body>\r\n            </Card>\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      <Modal show={showDeleteModal} onHide={closeDeleteModal} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Confirm Deletion</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {variantToDelete && (\r\n            <>\r\n              <p>Are you sure you want to delete this fabric variant?</p>\r\n              <Table bordered size=\"sm\" className=\"mt-3\">\r\n                <tbody>\r\n                  <tr>\r\n                    <td><strong>Color:</strong></td>\r\n                    <td>\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <div\r\n                          style={{\r\n                            width: '20px',\r\n                            height: '20px',\r\n                            backgroundColor: variantToDelete.color,\r\n                            borderRadius: '4px',\r\n                            border: '1px solid #dee2e6',\r\n                            marginRight: '10px'\r\n                          }}\r\n                        />\r\n                        {variantToDelete.color_name || variantToDelete.color}\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td><strong>Total Yard:</strong></td>\r\n                    <td>{variantToDelete.total_yard} yards</td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td><strong>Available Yard:</strong></td>\r\n                    <td>{variantToDelete.available_yard} yards</td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td><strong>Price per Yard:</strong></td>\r\n                    <td>Rs. {variantToDelete.price_per_yard}/yard</td>\r\n                  </tr>\r\n                </tbody>\r\n              </Table>\r\n\r\n              {hasDependencies && (\r\n                <Alert variant=\"warning\" className=\"mt-3\">\r\n                  <strong>Warning:</strong> This fabric variant cannot be deleted because it is being used in cutting records.\r\n                  <div className=\"mt-2\">\r\n                    <small>\r\n                      To delete this variant, you must first delete all cutting records that use it. You can view these records by clicking \"View Inventory\" and checking the cutting history.\r\n                    </small>\r\n                  </div>\r\n                </Alert>\r\n              )}\r\n\r\n              {!hasDependencies && fabricDetail && fabricDetail.variants && fabricDetail.variants.length > 1 && (\r\n                <Alert variant=\"success\" className=\"mt-3\">\r\n                  <strong>Good news!</strong> This fabric variant has no cutting records and can be safely deleted.\r\n                </Alert>\r\n              )}\r\n\r\n              {fabricDetail && (!fabricDetail.variants || fabricDetail.variants.length <= 1) && (\r\n                <Alert variant=\"warning\" className=\"mt-3\">\r\n                  <strong>Warning:</strong> Cannot delete the last variant of a fabric. If you want to remove this fabric completely, please delete the entire fabric definition from the fabric list.\r\n                </Alert>\r\n              )}\r\n            </>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={closeDeleteModal}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"danger\"\r\n            onClick={confirmDelete}\r\n            disabled={isDeleting || hasDependencies || (fabricDetail && fabricDetail.variants && fabricDetail.variants.length <= 1)}\r\n          >\r\n            {isDeleting ? (\r\n              <>\r\n                <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                Deleting...\r\n              </>\r\n            ) : (\r\n              'Delete Variant'\r\n            )}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* PDF Export Modal */}\r\n      <Modal show={showPdfModal} onHide={() => setShowPdfModal(false)}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <FaFilePdf className=\"me-2 text-primary\" />\r\n            Export Fabric Variants to PDF\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p className=\"text-muted mb-3\">\r\n            This will generate a PDF report containing all color variants of {fabricDetail?.fabric_name}.\r\n            The PDF will include fabric name, color, total yard, available yard, price per yard, and total value.\r\n          </p>\r\n\r\n          <Alert variant=\"info\">\r\n            <FaTable className=\"me-2\" />\r\n            <strong>Note:</strong> The PDF report will include all color variants for this fabric with their respective inventory details and color swatches.\r\n          </Alert>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowPdfModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={generatePDF}\r\n            disabled={pdfLoading}\r\n          >\r\n            {pdfLoading ? (\r\n              <>\r\n                <Spinner\r\n                  as=\"span\"\r\n                  animation=\"border\"\r\n                  size=\"sm\"\r\n                  role=\"status\"\r\n                  aria-hidden=\"true\"\r\n                  className=\"me-2\"\r\n                />\r\n                Generating PDF...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <FaFileDownload className=\"me-1\" /> Download PDF\r\n              </>\r\n            )}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Fabric Addition Report Modal */}\r\n      <Modal show={showAdditionPdfModal} onHide={() => setShowAdditionPdfModal(false)}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <FaFilePdf className=\"me-2 text-success\" />\r\n            Fabric Addition Report\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p className=\"text-muted mb-3\">\r\n            This will generate a PDF report showing the initial fabric addition details for {fabricDetail?.fabric_name}.\r\n            The report will include fabric name, supplier, date added, and details of all color variants that were added.\r\n          </p>\r\n\r\n          <Alert variant=\"info\">\r\n            <FaTable className=\"me-2\" />\r\n            <strong>Note:</strong> This report focuses on the initial fabric addition data rather than current inventory levels.\r\n          </Alert>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowAdditionPdfModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"success\"\r\n            onClick={generateAdditionPDF}\r\n            disabled={additionPdfLoading}\r\n          >\r\n            {additionPdfLoading ? (\r\n              <>\r\n                <Spinner\r\n                  as=\"span\"\r\n                  animation=\"border\"\r\n                  size=\"sm\"\r\n                  role=\"status\"\r\n                  aria-hidden=\"true\"\r\n                  className=\"me-2\"\r\n                />\r\n                Generating PDF...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <FaFileDownload className=\"me-1\" /> Download Report\r\n              </>\r\n            )}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ViewFabricVariants;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,WAAW,EAAEC,OAAO,QAAQ,eAAe;AACpD,SACEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EACxCC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EACpDC,IAAI,QACC,iBAAiB;AACxB,SACEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAChCC,iBAAiB,EAAEC,eAAe,EAAEC,SAAS,EAC7CC,aAAa,EAAEC,YAAY,EAAEC,OAAO,EAAEC,MAAM,EAC5CC,SAAS,EAAEC,cAAc,EAAEC,OAAO,QAC7B,gBAAgB;AACvB,SAASC,KAAK,QAAQ,OAAO;AAC7B,OAAOC,SAAS,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAG,CAAC,GAAGvC,SAAS,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAACuD,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAC5E,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1D,QAAQ,CAACO,OAAO,CAAC,mBAAmB,CAAC,CAAC;EAC1F,MAAMoD,QAAQ,GAAGvD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACwD,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8D,eAAe,EAAEC,kBAAkB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkE,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACoE,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC0E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2E,YAAY,GAAGA,CAAA,KAAM;MACzBtB,gBAAgB,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5C,CAAC;IAEDD,MAAM,CAACsB,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMrB,MAAM,CAACuB,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN3E,SAAS,CAAC,MAAM;IACdmD,UAAU,CAAC,IAAI,CAAC;IAChBlD,KAAK,CACF6E,GAAG,CAAC,gDAAgDrC,EAAE,GAAG,CAAC,CAC1DsC,IAAI,CAAEC,QAAQ,IAAK;MAClBrC,eAAe,CAACqC,QAAQ,CAACC,IAAI,CAAC;MAC9B9B,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACD+B,KAAK,CAAEpC,KAAK,IAAK;MAChBqC,OAAO,CAACrC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDD,UAAU,CAAC,+BAA+B,CAAC;MAC3CM,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,CAACV,EAAE,CAAC,CAAC;;EAER;EACA,MAAM2C,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,OAAO,GAAG;MAAEC,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAU,CAAC;IACnE,OAAO,IAAIC,IAAI,CAACL,UAAU,CAAC,CAACM,kBAAkB,CAACC,SAAS,EAAEN,OAAO,CAAC;EACpE,CAAC;;EAED;EACA,MAAMO,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACnD,YAAY,IAAI,CAACA,YAAY,CAACoD,QAAQ,EAAE,OAAO,CAAC;IACrD,OAAOpD,YAAY,CAACoD,QAAQ,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAK;MACtD,OAAOD,KAAK,GAAIC,OAAO,CAACC,UAAU,GAAGD,OAAO,CAACE,cAAe;IAC9D,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAC3D,YAAY,IAAI,CAACA,YAAY,CAACoD,QAAQ,EAAE,OAAO,CAAC;IACrD,OAAOpD,YAAY,CAACoD,QAAQ,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAK;MACtD,OAAOD,KAAK,GAAIC,OAAO,CAACK,cAAc,GAAGL,OAAO,CAACE,cAAe;IAClE,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC7D,YAAY,IAAI,CAACA,YAAY,CAACoD,QAAQ,EAAE,OAAO,CAAC;IACrD,OAAOpD,YAAY,CAACoD,QAAQ,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAK;MACtD,OAAOD,KAAK,GAAGQ,UAAU,CAACP,OAAO,CAACC,UAAU,CAAC;IAC/C,CAAC,EAAE,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMK,wBAAwB,GAAG,MAAOC,SAAS,IAAK;IACpD,IAAI;MACF;MACA;MACA,MAAM1B,QAAQ,GAAG,MAAM/E,KAAK,CAAC6E,GAAG,CAAC,oDAAoD,CAAC;MACtFK,OAAO,CAACwB,GAAG,CAAC,sBAAsB,EAAE3B,QAAQ,CAACC,IAAI,CAAC;;MAElD;MACA,IAAI2B,aAAa,GAAG,KAAK;MAEzB,KAAK,MAAMC,MAAM,IAAI7B,QAAQ,CAACC,IAAI,EAAE;QAClC,IAAI4B,MAAM,CAACC,OAAO,IAAID,MAAM,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;UAC/C;UACA,MAAMC,WAAW,GAAGH,MAAM,CAACC,OAAO,CAACG,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,cAAc,KAAKT,SAAS,CAAC;UAEtF,IAAIM,WAAW,EAAE;YACf7B,OAAO,CAACwB,GAAG,CAAC,WAAWD,SAAS,8BAA8BG,MAAM,CAACpE,EAAE,EAAE,CAAC;YAC1EmE,aAAa,GAAG,IAAI;YACpB;UACF;QACF;MACF;MAEA,IAAIA,aAAa,EAAE;QACjB,OAAO,IAAI;MACb;;MAEA;MACA,IAAI;QACF,MAAMQ,eAAe,GAAG,MAAMnH,KAAK,CAAC6E,GAAG,CAAC,oDAAoD4B,SAAS,WAAW,CAAC;QACjHvB,OAAO,CAACwB,GAAG,CAAC,oCAAoC,EAAES,eAAe,CAACnC,IAAI,CAAC;;QAEvE;QACA,IAAImC,eAAe,CAACnC,IAAI,IACpBmC,eAAe,CAACnC,IAAI,CAACoC,eAAe,IACpCD,eAAe,CAACnC,IAAI,CAACoC,eAAe,CAACN,MAAM,GAAG,CAAC,EAAE;UACnD5B,OAAO,CAACwB,GAAG,CAAC,WAAWD,SAAS,QAAQU,eAAe,CAACnC,IAAI,CAACoC,eAAe,CAACN,MAAM,kBAAkB,CAAC;UACtG,OAAO,IAAI;QACb;MACF,CAAC,CAAC,OAAOO,YAAY,EAAE;QACrBnC,OAAO,CAACrC,KAAK,CAAC,iCAAiC,EAAEwE,YAAY,CAAC;QAC9D;MACF;;MAEA;MACAnC,OAAO,CAACwB,GAAG,CAAC,WAAWD,SAAS,yBAAyB,CAAC;MAC1D,OAAO,KAAK;IACd,CAAC,CAAC,OAAO5D,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;;MAE5D;MACA;MACA,IAAIA,KAAK,CAACkC,QAAQ,IAAIlC,KAAK,CAACkC,QAAQ,CAACuC,MAAM,KAAK,GAAG,EAAE;QACnDpC,OAAO,CAACwB,GAAG,CAAC,WAAWD,SAAS,sCAAsC,CAAC;QACvE,OAAO,KAAK;MACd;;MAEA;MACAvB,OAAO,CAACrC,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACkC,QAAQ,GAAGlC,KAAK,CAACkC,QAAQ,CAACC,IAAI,GAAG,kBAAkB,CAAC;;MAE1F;MACA,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAMuC,iBAAiB,GAAG,MAAOvB,OAAO,IAAK;IAC3CnC,kBAAkB,CAACmC,OAAO,CAAC;IAC3BjC,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;;IAE3B,IAAI;MACF;MACA,IAAI,CAACxB,YAAY,CAACoD,QAAQ,IAAIpD,YAAY,CAACoD,QAAQ,CAACiB,MAAM,IAAI,CAAC,EAAE;QAC/DhE,QAAQ,CAAC,iGAAiG,CAAC;QAC3G;MACF;;MAEA;MACA,MAAM0E,OAAO,GAAG,MAAMhB,wBAAwB,CAACR,OAAO,CAACxD,EAAE,CAAC;MAC1DyB,kBAAkB,CAACuD,OAAO,CAAC;;MAE3B;MACA7D,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDC,QAAQ,CAAC,oFAAoF,CAAC;IAChG;EACF,CAAC;;EAED;EACA,MAAM2E,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAIzD,eAAe,EAAE;MACnB,OAAO,CAAC;IACV;IAEAD,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACFmB,OAAO,CAACwB,GAAG,CAAC,yCAAyC9C,eAAe,CAACpB,EAAE,EAAE,CAAC;;MAE1E;MACA,IAAI,CAACC,YAAY,CAACoD,QAAQ,IAAIpD,YAAY,CAACoD,QAAQ,CAACiB,MAAM,IAAI,CAAC,EAAE;QAC/DhE,QAAQ,CAAC,iGAAiG,CAAC;QAC3GiB,aAAa,CAAC,KAAK,CAAC;QACpB;MACF;;MAEA;MACA,IAAI;QACF,MAAMgB,QAAQ,GAAG,MAAM/E,KAAK,CAAC0H,MAAM,CAAC,6CAA6C9D,eAAe,CAACpB,EAAE,GAAG,CAAC;QACvG0C,OAAO,CAACwB,GAAG,CAAC,kBAAkB,EAAE3B,QAAQ,CAAC;;QAEzC;QACA,MAAM4C,eAAe,GAAGlF,YAAY,CAACoD,QAAQ,CAAC+B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrF,EAAE,KAAKoB,eAAe,CAACpB,EAAE,CAAC;QACtFE,eAAe,CAAC;UACd,GAAGD,YAAY;UACfoD,QAAQ,EAAE8B;QACZ,CAAC,CAAC;;QAEF;QACAhE,kBAAkB,CAAC,KAAK,CAAC;QACzBX,UAAU,CAAC,mBAAmBY,eAAe,CAACkE,UAAU,IAAIlE,eAAe,CAACmE,KAAK,yBAAyB,CAAC;;QAE3G;QACAC,UAAU,CAAC,MAAM;UACfhF,UAAU,CAAC,EAAE,CAAC;QAChB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC,OAAOiF,WAAW,EAAE;QACpB/C,OAAO,CAACrC,KAAK,CAAC,iCAAiC,EAAEoF,WAAW,CAAC;;QAE7D;QACA,IAAIA,WAAW,CAAClD,QAAQ,KAAKkD,WAAW,CAAClD,QAAQ,CAACuC,MAAM,KAAK,GAAG,IAAIW,WAAW,CAAClD,QAAQ,CAACuC,MAAM,KAAK,GAAG,CAAC,EAAE;UACxGpC,OAAO,CAACwB,GAAG,CAAC,yCAAyC,CAAC;UAEtD,IAAI;YACF;YACA,MAAM1G,KAAK,CAACkI,GAAG,CAAC,6CAA6CtE,eAAe,CAACpB,EAAE,GAAG,EAAE;cAClF2F,iBAAiB,EAAEvE,eAAe,CAACuE,iBAAiB;cACpDJ,KAAK,EAAEnE,eAAe,CAACmE,KAAK;cAC5BD,UAAU,EAAElE,eAAe,CAACkE,UAAU,IAAIlE,eAAe,CAACmE,KAAK;cAC/D9B,UAAU,EAAE,CAAC;cACbI,cAAc,EAAE,CAAC;cACjBH,cAAc,EAAE;YAClB,CAAC,CAAC;;YAEF;YACA,MAAMlG,KAAK,CAAC0H,MAAM,CAAC,6CAA6C9D,eAAe,CAACpB,EAAE,GAAG,CAAC;;YAEtF;YACA,MAAMmF,eAAe,GAAGlF,YAAY,CAACoD,QAAQ,CAAC+B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrF,EAAE,KAAKoB,eAAe,CAACpB,EAAE,CAAC;YACtFE,eAAe,CAAC;cACd,GAAGD,YAAY;cACfoD,QAAQ,EAAE8B;YACZ,CAAC,CAAC;;YAEF;YACAhE,kBAAkB,CAAC,KAAK,CAAC;YACzBX,UAAU,CAAC,mBAAmBY,eAAe,CAACkE,UAAU,IAAIlE,eAAe,CAACmE,KAAK,yBAAyB,CAAC;;YAE3G;YACAC,UAAU,CAAC,MAAM;cACfhF,UAAU,CAAC,EAAE,CAAC;YAChB,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,CAAC,OAAOoF,gBAAgB,EAAE;YACzBlD,OAAO,CAACrC,KAAK,CAAC,mCAAmC,EAAEuF,gBAAgB,CAAC;YACpE,MAAMA,gBAAgB,CAAC,CAAC;UAC1B;QACF,CAAC,MAAM;UACL,MAAMH,WAAW,CAAC,CAAC;QACrB;MACF;IACF,CAAC,CAAC,OAAOpF,KAAK,EAAE;MAAA,IAAAwF,eAAA,EAAAC,oBAAA;MACdpD,OAAO,CAACrC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACAqC,OAAO,CAACrC,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACkC,QAAQ,GAAGlC,KAAK,CAACkC,QAAQ,CAACC,IAAI,GAAG,kBAAkB,CAAC;MAC1FE,OAAO,CAACrC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACkC,QAAQ,GAAGlC,KAAK,CAACkC,QAAQ,CAACuC,MAAM,GAAG,WAAW,CAAC;;MAEpF;MACA,IAAIiB,YAAY,GAAG,oDAAoD;MAEvE,IAAI1F,KAAK,CAACkC,QAAQ,EAAE;QAClB,IAAIlC,KAAK,CAACkC,QAAQ,CAACuC,MAAM,KAAK,GAAG,EAAE;UACjCiB,YAAY,GAAG1F,KAAK,CAACkC,QAAQ,CAACC,IAAI,CAACiC,MAAM,IAAI,8DAA8D;QAC7G,CAAC,MAAM,IAAIpE,KAAK,CAACkC,QAAQ,CAACuC,MAAM,KAAK,GAAG,EAAE;UACxCiB,YAAY,GAAG,mDAAmD;QACpE,CAAC,MAAM,IAAI1F,KAAK,CAACkC,QAAQ,CAACuC,MAAM,KAAK,GAAG,EAAE;UACxCiB,YAAY,GAAG,sDAAsD;QACvE,CAAC,MAAM,IAAI1F,KAAK,CAACkC,QAAQ,CAACuC,MAAM,KAAK,GAAG,EAAE;UACxCiB,YAAY,GAAG,iDAAiD;QAClE;MACF;MAEAzF,QAAQ,CAAC,EAAAuF,eAAA,GAAAxF,KAAK,CAACkC,QAAQ,cAAAsD,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBrD,IAAI,cAAAsD,oBAAA,uBAApBA,oBAAA,CAAsB3F,OAAO,KAAI4F,YAAY,CAAC;;MAEvD;MACAP,UAAU,CAAC,MAAM;QACflF,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,SAAS;MACRiB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMyE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B7E,kBAAkB,CAAC,KAAK,CAAC;IACzBE,kBAAkB,CAAC,IAAI,CAAC;IACxBI,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMwE,YAAY,GAAGA,CAAA,KAAM;IACzBtE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuE,oBAAoB,GAAGA,CAAA,KAAM;IACjCnE,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMoE,WAAW,GAAGA,CAAA,KAAM;IACxBtE,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI,CAAC5B,YAAY,IAAI,CAACA,YAAY,CAACoD,QAAQ,EAAE;MAC3C/C,QAAQ,CAAC,oCAAoC,CAAC;MAC9CuB,aAAa,CAAC,KAAK,CAAC;MACpB;IACF;IAEA,IAAI;MACF;MACA,MAAMuE,GAAG,GAAG,IAAI5G,KAAK,CAAC;QACpB6G,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE;MACV,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG,EAAE;MACxB,MAAMC,eAAe,GAAG,EAAE;MAC1B,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,aAAa,GAAG,CAAC;;MAEvB;MACA,IAAI;QACF;QACA,MAAMC,OAAO,GAAG/F,MAAM,CAACgG,QAAQ,CAACC,MAAM;;QAEtC;QACAV,GAAG,CAACW,QAAQ,CAAC,GAAGH,OAAO,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC5D,CAAC,CAAC,OAAOI,SAAS,EAAE;QAClBtE,OAAO,CAACuE,IAAI,CAAC,4BAA4B,EAAED,SAAS,CAAC;;QAErD;QACAZ,GAAG,CAACc,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAChCd,GAAG,CAACe,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;;QAE7B;QACAf,GAAG,CAACgB,WAAW,CAAC,EAAE,CAAC;QACnBhB,GAAG,CAACiB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/BjB,GAAG,CAACkB,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QAC3CnB,GAAG,CAACiB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC7B;;MAEA;MACAjB,GAAG,CAACgB,WAAW,CAACZ,aAAa,CAAC;MAC9BJ,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACkB,IAAI,CAAC,mCAAmC,EAAE,GAAG,EAAE,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAE3E;MACAnB,GAAG,CAACgB,WAAW,CAACX,eAAe,CAAC;MAChCL,GAAG,CAACkB,IAAI,CAAC,oBAAoB,EAAE,EAAE,EAAE,EAAE,CAAC;MAEtClB,GAAG,CAACgB,WAAW,CAACV,cAAc,CAAC;MAC/BN,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;;MAElC;MACA,MAAMC,UAAU,GAAG,CACjB,CAAC,aAAa,EAAExH,YAAY,CAACyH,WAAW,IAAI,KAAK,CAAC,EAClD,CAAC,WAAW,EAAEzH,YAAY,CAAC0H,cAAc,IAAI1H,YAAY,CAAC0H,cAAc,CAACrD,MAAM,GAAG,CAAC,GAC/ErE,YAAY,CAAC0H,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,GACtC,cAAc,CAAC,EACnB,CAAC,YAAY,EAAEjF,UAAU,CAAC1C,YAAY,CAAC4H,UAAU,CAAC,IAAI,KAAK,CAAC,EAC5D,CAAC,gBAAgB,EAAE,CAAC5H,YAAY,CAACoD,QAAQ,GAAGpD,YAAY,CAACoD,QAAQ,CAACiB,MAAM,GAAG,CAAC,EAAEwD,QAAQ,CAAC,CAAC,CAAC,EACzF,CAAC,aAAa,EAAEhE,mBAAmB,CAAC,CAAC,CAACgE,QAAQ,CAAC,CAAC,CAAC,EACjD,CAAC,iBAAiB,EAAE,CAAC7H,YAAY,CAACoD,QAAQ,IAAI,EAAE,EAAEC,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAKD,KAAK,GAAGQ,UAAU,CAACP,OAAO,CAACK,cAAc,CAAC,EAAE,CAAC,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC,EACvI,CAAC,mBAAmB,EAAE,OAAOP,mBAAmB,CAAC,CAAC,EAAE,CAAC,EACrD,CAAC,eAAe,EAAE,OAAOQ,qBAAqB,CAAC,CAAC,EAAE,CAAC,CACpD;;MAED;MACAnE,SAAS,CAAC2G,GAAG,EAAE;QACb2B,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC7BC,IAAI,EAAER,UAAU;QAChBS,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE;QAAI,CAAC;QACzDC,MAAM,EAAE;UAAEC,QAAQ,EAAE;QAAG;MACzB,CAAC,CAAC;;MAEF;MACAnC,GAAG,CAACgB,WAAW,CAACX,eAAe,CAAC;MAChCL,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACkB,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAElB,GAAG,CAACoC,aAAa,CAACC,MAAM,GAAG,EAAE,CAAC;;MAE7D;MACA,MAAMC,YAAY,GAAG,CAACzI,YAAY,CAACoD,QAAQ,IAAI,EAAE,EAAEsF,GAAG,CAACnF,OAAO,IAAI;QAChE;QACA,MAAMoF,YAAY,GAAG,CAACpF,OAAO,CAACK,cAAc,GAAGL,OAAO,CAACE,cAAc,EAAEC,OAAO,CAAC,CAAC,CAAC;QACjF,OAAO,CACLH,OAAO,CAAC8B,UAAU,IAAI9B,OAAO,CAAC+B,KAAK,IAAI,KAAK,EAC5C,EAAE;QAAE;QACJ/B,OAAO,CAAC+B,KAAK,IAAI,KAAK;QAAE;QACxB/B,OAAO,CAACC,UAAU,CAACqE,QAAQ,CAAC,CAAC,EAC7BtE,OAAO,CAACK,cAAc,CAACiE,QAAQ,CAAC,CAAC,EACjC,OAAOtE,OAAO,CAACE,cAAc,EAAE,EAC/B,OAAOkF,YAAY,EAAE,CACtB;MACH,CAAC,CAAC;;MAEF;;MAEA;MACAnJ,SAAS,CAAC2G,GAAG,EAAE;QACb2B,MAAM,EAAE3B,GAAG,CAACoC,aAAa,CAACC,MAAM,GAAG,EAAE;QACrCT,IAAI,EAAE,CAAC,CAAC,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;QAC5GC,IAAI,EAAES,YAAY;QAClBR,KAAK,EAAE,SAAS;QAChBI,MAAM,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAC;QACxBJ,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE;QAAI,CAAC;QACzDQ,YAAY,EAAE;UACZ,CAAC,EAAE;YAAEC,SAAS,EAAE;UAAG,CAAC;UAAE;UACtB,CAAC,EAAE;YAAEA,SAAS,EAAE;UAAG,CAAC,CAAE;QACxB,CAAC;QACDC,WAAW,EAAGvG,IAAI,IAAK;UACrB;UACA,IAAIA,IAAI,CAACwG,OAAO,KAAK,MAAM,IAAIxG,IAAI,CAACyG,MAAM,CAACC,KAAK,KAAK,CAAC,EAAE;YACtD,MAAMC,QAAQ,GAAG3G,IAAI,CAAC4G,GAAG,CAACF,KAAK;YAC/B,MAAMG,SAAS,GAAGX,YAAY,CAACS,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAE7C;YACA,IAAIE,SAAS,IAAIA,SAAS,KAAK,KAAK,EAAE;cACpC,MAAM;gBAAEC,CAAC;gBAAEC,CAAC;gBAAEC,KAAK;gBAAEC;cAAO,CAAC,GAAGjH,IAAI,CAACkH,IAAI;;cAEzC;cACA,MAAMC,OAAO,GAAG,CAAC;cACjB,MAAMC,OAAO,GAAGN,CAAC,GAAGK,OAAO;cAC3B,MAAME,OAAO,GAAGN,CAAC,GAAGI,OAAO;cAC3B,MAAMG,WAAW,GAAGN,KAAK,GAAIG,OAAO,GAAG,CAAE;cACzC,MAAMI,YAAY,GAAGN,MAAM,GAAIE,OAAO,GAAG,CAAE;;cAE3C;cACAvD,GAAG,CAACc,YAAY,CAACmC,SAAS,CAAC;cAC3BjD,GAAG,CAACe,IAAI,CAACyC,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAE,GAAG,CAAC;;cAE1D;cACA3D,GAAG,CAAC4D,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cACjC5D,GAAG,CAACe,IAAI,CAACyC,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAE,GAAG,CAAC;YAC5D;UACF;QACF;MACF,CAAC,CAAC;;MAEF;MACA3D,GAAG,CAACgB,WAAW,CAACT,aAAa,CAAC;MAC9BP,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCpB,GAAG,CAACkB,IAAI,CAAC,iBAAiB,IAAIrE,IAAI,CAAC,CAAC,CAACgH,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;QAAE1C,KAAK,EAAE;MAAS,CAAC,CAAC;MACvFnB,GAAG,CAACkB,IAAI,CAAC,uCAAuC,EAAE,GAAG,EAAE,GAAG,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAEhF;MACA,MAAM2C,eAAe,GAAGjK,YAAY,CAACyH,WAAW,CAACyC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;MAC9E/D,GAAG,CAACgE,IAAI,CAAC,oBAAoBF,eAAe,IAAI,IAAIjH,IAAI,CAAC,CAAC,CAACoH,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;MAE5FzI,aAAa,CAAC,KAAK,CAAC;MACpBF,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,2BAA2BD,KAAK,CAACF,OAAO,EAAE,CAAC;MACpD0B,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAM0I,mBAAmB,GAAGA,CAAA,KAAM;IAChCtI,qBAAqB,CAAC,IAAI,CAAC;IAE3B,IAAI,CAAChC,YAAY,IAAI,CAACA,YAAY,CAACoD,QAAQ,EAAE;MAC3C/C,QAAQ,CAAC,oCAAoC,CAAC;MAC9C2B,qBAAqB,CAAC,KAAK,CAAC;MAC5B;IACF;IAEA,IAAI;MACF;MACA,MAAMmE,GAAG,GAAG,IAAI5G,KAAK,CAAC;QACpB6G,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE;MACV,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG,EAAE;MACxB,MAAMC,eAAe,GAAG,EAAE;MAC1B,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,aAAa,GAAG,CAAC;;MAEvB;MACA,IAAI;QACF;QACA,MAAMC,OAAO,GAAG/F,MAAM,CAACgG,QAAQ,CAACC,MAAM;;QAEtC;QACAV,GAAG,CAACW,QAAQ,CAAC,GAAGH,OAAO,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC5D,CAAC,CAAC,OAAOI,SAAS,EAAE;QAClBtE,OAAO,CAACuE,IAAI,CAAC,4BAA4B,EAAED,SAAS,CAAC;;QAErD;QACAZ,GAAG,CAACc,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAChCd,GAAG,CAACe,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;;QAE7B;QACAf,GAAG,CAACgB,WAAW,CAAC,EAAE,CAAC;QACnBhB,GAAG,CAACiB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/BjB,GAAG,CAACkB,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QAC3CnB,GAAG,CAACiB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC7B;;MAEA;MACAjB,GAAG,CAACgB,WAAW,CAACZ,aAAa,CAAC;MAC9BJ,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACkB,IAAI,CAAC,wBAAwB,EAAE,GAAG,EAAE,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAEhE;MACAnB,GAAG,CAACgB,WAAW,CAACX,eAAe,CAAC;MAChCL,GAAG,CAACkB,IAAI,CAAC,oBAAoB,EAAE,EAAE,EAAE,EAAE,CAAC;MAEtClB,GAAG,CAACgB,WAAW,CAACV,cAAc,CAAC;MAC/BN,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;;MAElC;MACA,MAAMC,UAAU,GAAG,CACjB,CAAC,aAAa,EAAExH,YAAY,CAACyH,WAAW,IAAI,KAAK,CAAC,EAClD,CAAC,UAAU,EAAEzH,YAAY,CAACuK,aAAa,IAAI,KAAK,CAAC,EACjD,CAAC,YAAY,EAAE7H,UAAU,CAAC1C,YAAY,CAAC4H,UAAU,CAAC,IAAI,KAAK,CAAC,EAC5D,CAAC,gBAAgB,EAAE5H,YAAY,CAACoD,QAAQ,CAACiB,MAAM,CAACwD,QAAQ,CAAC,CAAC,CAAC,CAC5D;;MAED;MACArI,SAAS,CAAC2G,GAAG,EAAE;QACb2B,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC7BC,IAAI,EAAER,UAAU;QAChBS,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE;QAAI,CAAC;QACzDC,MAAM,EAAE;UAAEC,QAAQ,EAAE;QAAG;MACzB,CAAC,CAAC;;MAEF;MACAnC,GAAG,CAACgB,WAAW,CAACX,eAAe,CAAC;MAChCL,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACkB,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAElB,GAAG,CAACoC,aAAa,CAACC,MAAM,GAAG,EAAE,CAAC;;MAEpE;MACA,MAAMC,YAAY,GAAG,CAACzI,YAAY,CAACoD,QAAQ,IAAI,EAAE,EAAEsF,GAAG,CAACnF,OAAO,IAAI;QAChE,OAAO,CACLA,OAAO,CAAC8B,UAAU,IAAI9B,OAAO,CAAC+B,KAAK,IAAI,KAAK,EAC5C,EAAE;QAAE;QACJ/B,OAAO,CAAC+B,KAAK,IAAI,KAAK;QAAE;QACxB/B,OAAO,CAACC,UAAU,CAACqE,QAAQ,CAAC,CAAC,EAC7B,OAAOtE,OAAO,CAACE,cAAc,EAAE,EAC/B,OAAO,CAACF,OAAO,CAACC,UAAU,GAAGD,OAAO,CAACE,cAAc,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAE,CAClE;MACH,CAAC,CAAC;;MAEF;;MAEA;MACAlE,SAAS,CAAC2G,GAAG,EAAE;QACb2B,MAAM,EAAE3B,GAAG,CAACoC,aAAa,CAACC,MAAM,GAAG,EAAE;QACrCT,IAAI,EAAE,CAAC,CAAC,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QACxFC,IAAI,EAAES,YAAY;QAClBR,KAAK,EAAE,SAAS;QAChBI,MAAM,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAC;QACxBJ,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE;QAAI,CAAC;QACzDQ,YAAY,EAAE;UACZ,CAAC,EAAE;YAAEC,SAAS,EAAE;UAAG,CAAC;UAAE;UACtB,CAAC,EAAE;YAAEA,SAAS,EAAE;UAAG,CAAC,CAAE;QACxB,CAAC;QACDC,WAAW,EAAGvG,IAAI,IAAK;UACrB;UACA,IAAIA,IAAI,CAACwG,OAAO,KAAK,MAAM,IAAIxG,IAAI,CAACyG,MAAM,CAACC,KAAK,KAAK,CAAC,EAAE;YACtD,MAAMC,QAAQ,GAAG3G,IAAI,CAAC4G,GAAG,CAACF,KAAK;YAC/B,MAAMG,SAAS,GAAGX,YAAY,CAACS,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAE7C;YACA,IAAIE,SAAS,IAAIA,SAAS,KAAK,KAAK,EAAE;cACpC,MAAM;gBAAEC,CAAC;gBAAEC,CAAC;gBAAEC,KAAK;gBAAEC;cAAO,CAAC,GAAGjH,IAAI,CAACkH,IAAI;;cAEzC;cACA,MAAMC,OAAO,GAAG,CAAC;cACjB,MAAMC,OAAO,GAAGN,CAAC,GAAGK,OAAO;cAC3B,MAAME,OAAO,GAAGN,CAAC,GAAGI,OAAO;cAC3B,MAAMG,WAAW,GAAGN,KAAK,GAAIG,OAAO,GAAG,CAAE;cACzC,MAAMI,YAAY,GAAGN,MAAM,GAAIE,OAAO,GAAG,CAAE;;cAE3C;cACAvD,GAAG,CAACc,YAAY,CAACmC,SAAS,CAAC;cAC3BjD,GAAG,CAACe,IAAI,CAACyC,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAE,GAAG,CAAC;;cAE1D;cACA3D,GAAG,CAAC4D,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cACjC5D,GAAG,CAACe,IAAI,CAACyC,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAE,GAAG,CAAC;YAC5D;UACF;QACF;MACF,CAAC,CAAC;;MAEF;MACA3D,GAAG,CAACgB,WAAW,CAACX,eAAe,CAAC;MAChCL,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCpB,GAAG,CAACkB,IAAI,CAAC,SAAS,EAAE,EAAE,EAAElB,GAAG,CAACoC,aAAa,CAACC,MAAM,GAAG,EAAE,CAAC;;MAEtD;MACA,MAAMgC,UAAU,GAAG3G,mBAAmB,CAAC,CAAC;MACxC,MAAM4G,UAAU,GAAGtH,mBAAmB,CAAC,CAAC;;MAExC;MACA3D,SAAS,CAAC2G,GAAG,EAAE;QACb2B,MAAM,EAAE3B,GAAG,CAACoC,aAAa,CAACC,MAAM,GAAG,EAAE;QACrCT,IAAI,EAAE,CAAC,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;QAC5CC,IAAI,EAAE,CAAC,CAACwC,UAAU,EAAE,OAAOC,UAAU,EAAE,CAAC,CAAC;QACzCxC,KAAK,EAAE,MAAM;QACbI,MAAM,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEoC,MAAM,EAAE;QAAS,CAAC;QAC1CxC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE;QAAI;MAC1D,CAAC,CAAC;;MAEF;MACAjC,GAAG,CAACgB,WAAW,CAACT,aAAa,CAAC;MAC9BP,GAAG,CAACoB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCpB,GAAG,CAACkB,IAAI,CAAC,iBAAiB,IAAIrE,IAAI,CAAC,CAAC,CAACgH,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;QAAE1C,KAAK,EAAE;MAAS,CAAC,CAAC;MACvFnB,GAAG,CAACkB,IAAI,CAAC,uCAAuC,EAAE,GAAG,EAAE,GAAG,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAEhF;MACA,MAAM2C,eAAe,GAAGjK,YAAY,CAACyH,WAAW,CAACyC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;MAC9E/D,GAAG,CAACgE,IAAI,CAAC,mBAAmBF,eAAe,IAAI,IAAIjH,IAAI,CAAC,CAAC,CAACoH,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;MAE3FrI,qBAAqB,CAAC,KAAK,CAAC;MAC5BF,uBAAuB,CAAC,KAAK,CAAC;IAChC,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,2BAA2BD,KAAK,CAACF,OAAO,EAAE,CAAC;MACpD8B,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,IAAIxB,OAAO,EAAE;IACX,oBACEd,OAAA,CAAAE,SAAA;MAAA+K,QAAA,gBACEjL,OAAA,CAAChC,eAAe;QAAAkN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnBrL,OAAA,CAAC7B,SAAS;QAACmN,KAAK;QACdC,KAAK,EAAE;UACLC,UAAU,EAAExK,aAAa,GAAG,OAAO,GAAG,MAAM;UAC5C6I,KAAK,EAAE,eAAe7I,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG;UACzDyK,UAAU,EAAE,eAAe;UAC3BzB,OAAO,EAAE;QACX,CAAE;QAAAiB,QAAA,eAEFjL,OAAA;UAAK0L,SAAS,EAAC,kBAAkB;UAAAT,QAAA,gBAC/BjL,OAAA,CAACrB,OAAO;YAACgN,SAAS,EAAC,QAAQ;YAACC,IAAI,EAAC,QAAQ;YAAC/H,OAAO,EAAC,SAAS;YAAAoH,QAAA,eACzDjL,OAAA;cAAM0L,SAAS,EAAC,iBAAiB;cAAAT,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACVrL,OAAA;YAAG0L,SAAS,EAAC,MAAM;YAAAT,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA,eACZ,CAAC;EAEP;EAEA,IAAI,CAAC/K,YAAY,EAAE;IACjB,oBACEN,OAAA,CAAAE,SAAA;MAAA+K,QAAA,gBACEjL,OAAA,CAAChC,eAAe;QAAAkN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnBrL,OAAA,CAAC7B,SAAS;QAACmN,KAAK;QACdC,KAAK,EAAE;UACLC,UAAU,EAAExK,aAAa,GAAG,OAAO,GAAG,MAAM;UAC5C6I,KAAK,EAAE,eAAe7I,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG;UACzDyK,UAAU,EAAE,eAAe;UAC3BzB,OAAO,EAAE;QACX,CAAE;QAAAiB,QAAA,gBAEFjL,OAAA,CAACtB,KAAK;UAACmF,OAAO,EAAC,QAAQ;UAAC6H,SAAS,EAAC,aAAa;UAAAT,QAAA,EAC5CzK,OAAO,IAAI;QAA+B;UAAA0K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACRrL,OAAA;UAAK0L,SAAS,EAAC,kBAAkB;UAAAT,QAAA,eAC/BjL,OAAA,CAACxB,MAAM;YAACqF,OAAO,EAAC,WAAW;YAACgI,OAAO,EAAEA,CAAA,KAAMvK,QAAQ,CAAC,CAAC,CAAC,CAAE;YAAA2J,QAAA,gBACtDjL,OAAA,CAACf,WAAW;cAACyM,SAAS,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBAClC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA,eACZ,CAAC;EAEP;EAEA,oBACErL,OAAA,CAAAE,SAAA;IAAA+K,QAAA,gBACEjL,OAAA,CAAChC,eAAe;MAAAkN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBrL,OAAA,CAAC7B,SAAS;MAACmN,KAAK;MACdC,KAAK,EAAE;QACLC,UAAU,EAAExK,aAAa,GAAG,OAAO,GAAG,MAAM;QAC5C6I,KAAK,EAAE,eAAe7I,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG;QACzDyK,UAAU,EAAE,eAAe;QAC3BzB,OAAO,EAAE;MACX,CAAE;MAAAiB,QAAA,gBAEFjL,OAAA;QAAK0L,SAAS,EAAC,wDAAwD;QAAAT,QAAA,gBACrEjL,OAAA,CAACxB,MAAM;UACLqF,OAAO,EAAC,mBAAmB;UAC3BgI,OAAO,EAAEA,CAAA,KAAMvK,QAAQ,CAAC,CAAC,CAAC,CAAE;UAC5BoK,SAAS,EAAC,MAAM;UAAAT,QAAA,gBAEhBjL,OAAA,CAACf,WAAW;YAACyM,SAAS,EAAC;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAClC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrL,OAAA;UAAI0L,SAAS,EAAC,8BAA8B;UAAAT,QAAA,gBAC1CjL,OAAA,CAAChB,QAAQ;YAAC0M,SAAS,EAAC;UAAmB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACzC/K,YAAY,GAAGA,YAAY,CAACyH,WAAW,GAAG,YAAY;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACLrL,OAAA;UAAAiL,QAAA,gBACEjL,OAAA,CAACxB,MAAM;YACLqF,OAAO,EAAC,iBAAiB;YACzBgI,OAAO,EAAEvF,YAAa;YACtBoF,SAAS,EAAC,MAAM;YAAAT,QAAA,gBAEhBjL,OAAA,CAACN,SAAS;cAACgM,SAAS,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAChC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrL,OAAA,CAACxB,MAAM;YACLqF,OAAO,EAAC,iBAAiB;YACzBgI,OAAO,EAAEtF,oBAAqB;YAC9BmF,SAAS,EAAC,MAAM;YAAAT,QAAA,gBAEhBjL,OAAA,CAACN,SAAS;cAACgM,SAAS,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBAChC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL7K,OAAO,iBAAIR,OAAA,CAACtB,KAAK;QAACmF,OAAO,EAAC,QAAQ;QAAC6H,SAAS,EAAC,aAAa;QAAAT,QAAA,EAAEzK;MAAO;QAAA0K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAC5E3K,KAAK,iBAAIV,OAAA,CAACtB,KAAK;QAACmF,OAAO,EAAC,QAAQ;QAAC6H,SAAS,EAAC,aAAa;QAAAT,QAAA,EAAEvK;MAAK;QAAAwK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACxEzK,OAAO,iBAAIZ,OAAA,CAACtB,KAAK;QAACmF,OAAO,EAAC,SAAS;QAAC6H,SAAS,EAAC,aAAa;QAAAT,QAAA,EAAErK;MAAO;QAAAsK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAE9ErL,OAAA,CAAC5B,GAAG;QAACsN,SAAS,EAAC,MAAM;QAAAT,QAAA,gBACnBjL,OAAA,CAAC3B,GAAG;UAACyN,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eACjBjL,OAAA,CAAC1B,IAAI;YAACoN,SAAS,EAAC,iBAAiB;YAAAT,QAAA,gBAC/BjL,OAAA,CAAC1B,IAAI,CAAC0N,MAAM;cAACN,SAAS,EAAC,uBAAuB;cAAAT,QAAA,eAC5CjL,OAAA;gBAAI0L,SAAS,EAAC,MAAM;gBAAAT,QAAA,gBAClBjL,OAAA,CAACT,YAAY;kBAACmM,SAAS,EAAC;gBAAM;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sBAEnC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACdrL,OAAA,CAAC1B,IAAI,CAAC2N,IAAI;cAAAhB,QAAA,eACRjL,OAAA,CAACpB,SAAS;gBAACiF,OAAO,EAAC,OAAO;gBAAAoH,QAAA,gBACxBjL,OAAA,CAACpB,SAAS,CAACsN,IAAI;kBAACR,SAAS,EAAC,mDAAmD;kBAAAT,QAAA,gBAC3EjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA,CAACX,SAAS;sBAACqM,SAAS,EAAC;oBAAqB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,aAE/C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrL,OAAA;oBAAK0L,SAAS,EAAC,UAAU;oBAAAT,QAAA,EACtB3K,YAAY,CAAC0H,cAAc,IAAI1H,YAAY,CAAC0H,cAAc,CAACrD,MAAM,GAAG,CAAC,GAClErE,YAAY,CAAC0H,cAAc,CAACgB,GAAG,CAAC,CAACmD,IAAI,EAAE5C,KAAK,kBAC1CvJ,OAAA,CAACvB,KAAK;sBAAa2N,EAAE,EAAC,MAAM;sBAACC,IAAI;sBAACX,SAAS,EAAC,WAAW;sBAAAT,QAAA,EAAEkB;oBAAI,GAAjD5C,KAAK;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAoD,CACtE,CAAC,gBACFrL,OAAA,CAACvB,KAAK;sBAAC2N,EAAE,EAAC,WAAW;sBAACC,IAAI;sBAAApB,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACjBrL,OAAA,CAACpB,SAAS,CAACsN,IAAI;kBAACR,SAAS,EAAC,mDAAmD;kBAAAT,QAAA,gBAC3EjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA,CAACV,aAAa;sBAACoM,SAAS,EAAC;oBAAqB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,cAEnD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrL,OAAA;oBAAAiL,QAAA,EAAOjI,UAAU,CAAC1C,YAAY,CAAC4H,UAAU;kBAAC;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACjBrL,OAAA,CAACpB,SAAS,CAACsN,IAAI;kBAACR,SAAS,EAAC,mDAAmD;kBAAAT,QAAA,gBAC3EjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA,CAACd,SAAS;sBAACwM,SAAS,EAAC;oBAAqB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,kBAE/C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrL,OAAA,CAACvB,KAAK;oBAAC2N,EAAE,EAAC,SAAS;oBAACC,IAAI;oBAAApB,QAAA,EAAE3K,YAAY,CAACoD,QAAQ,GAAGpD,YAAY,CAACoD,QAAQ,CAACiB,MAAM,GAAG;kBAAC;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACjBrL,OAAA,CAACpB,SAAS,CAACsN,IAAI;kBAACR,SAAS,EAAC,mDAAmD;kBAAAT,QAAA,gBAC3EjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA,CAACb,iBAAiB;sBAACuM,SAAS,EAAC;oBAAqB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAEvD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrL,OAAA;oBAAAiL,QAAA,GAAO9G,mBAAmB,CAAC,CAAC,EAAC,QAAM;kBAAA;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACjBrL,OAAA,CAACpB,SAAS,CAACsN,IAAI;kBAACR,SAAS,EAAC,mDAAmD;kBAAAT,QAAA,gBAC3EjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA,CAACb,iBAAiB;sBAACuM,SAAS,EAAC;oBAAqB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,mBAEvD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrL,OAAA;oBAAAiL,QAAA,GAAO,CAAC3K,YAAY,CAACoD,QAAQ,IAAI,EAAE,EAAEC,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAKD,KAAK,GAAGQ,UAAU,CAACP,OAAO,CAACK,cAAc,CAAC,EAAE,CAAC,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,EAAC,QAAM;kBAAA;oBAAAkH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC,eACjBrL,OAAA,CAACpB,SAAS,CAACsN,IAAI;kBAACR,SAAS,EAAC,mDAAmD;kBAAAT,QAAA,gBAC3EjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA,CAACZ,eAAe;sBAACsM,SAAS,EAAC;oBAAqB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,qBAErD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrL,OAAA;oBAAM0L,SAAS,EAAC,sBAAsB;oBAAAT,QAAA,GAAC,MAAI,EAACxH,mBAAmB,CAAC,CAAC;kBAAA;oBAAAyH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACjBrL,OAAA,CAACpB,SAAS,CAACsN,IAAI;kBAACR,SAAS,EAAC,mDAAmD;kBAAAT,QAAA,gBAC3EjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA,CAACZ,eAAe;sBAACsM,SAAS,EAAC;oBAAqB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,iBAErD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrL,OAAA;oBAAM0L,SAAS,EAAC,sBAAsB;oBAAAT,QAAA,GAAC,MAAI,EAAChH,qBAAqB,CAAC,CAAC;kBAAA;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENrL,OAAA,CAAC3B,GAAG;UAACyN,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eACjBjL,OAAA,CAAC1B,IAAI;YAACoN,SAAS,EAAC,WAAW;YAAAT,QAAA,gBACzBjL,OAAA,CAAC1B,IAAI,CAAC0N,MAAM;cAACN,SAAS,EAAC,uBAAuB;cAAAT,QAAA,eAC5CjL,OAAA;gBAAI0L,SAAS,EAAC,MAAM;gBAAAT,QAAA,gBAClBjL,OAAA,CAACd,SAAS;kBAACwM,SAAS,EAAC;gBAAM;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kBAEhC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACdrL,OAAA,CAAC1B,IAAI,CAAC2N,IAAI;cAAAhB,QAAA,eACRjL,OAAA,CAACzB,KAAK;gBAAC+N,KAAK;gBAACC,QAAQ;gBAACC,UAAU;gBAACd,SAAS,EAAC,cAAc;gBAAAT,QAAA,gBACvDjL,OAAA;kBAAO0L,SAAS,EAAC,UAAU;kBAAAT,QAAA,eACzBjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA;sBAAIuL,KAAK,EAAE;wBAAE1B,KAAK,EAAE;sBAAM,CAAE;sBAAAoB,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvCrL,OAAA;sBAAIuL,KAAK,EAAE;wBAAE1B,KAAK,EAAE;sBAAM,CAAE;sBAAC6B,SAAS,EAAC,aAAa;sBAAAT,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1ErL,OAAA;sBAAIuL,KAAK,EAAE;wBAAE1B,KAAK,EAAE;sBAAM,CAAE;sBAAC6B,SAAS,EAAC,aAAa;sBAAAT,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxErL,OAAA;sBAAIuL,KAAK,EAAE;wBAAE1B,KAAK,EAAE;sBAAM,CAAE;sBAAC6B,SAAS,EAAC,aAAa;sBAAAT,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACtEjK,kBAAkB,iBACjBpB,OAAA;sBAAIuL,KAAK,EAAE;wBAAE1B,KAAK,EAAE;sBAAM,CAAE;sBAAC6B,SAAS,EAAC,aAAa;sBAAAT,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CACjE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACRrL,OAAA;kBAAAiL,QAAA,EACG,CAAC3K,YAAY,CAACoD,QAAQ,IAAI,EAAE,EAAEsF,GAAG,CAAEnF,OAAO,iBACzC7D,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA;sBAAAiL,QAAA,eACEjL,OAAA;wBAAK0L,SAAS,EAAC,2BAA2B;wBAAAT,QAAA,gBACxCjL,OAAA;0BACEuL,KAAK,EAAE;4BACL1B,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACd2C,eAAe,EAAE5I,OAAO,CAAC+B,KAAK;4BAC9B8G,YAAY,EAAE,KAAK;4BACnBC,MAAM,EAAE,mBAAmB;4BAC3BC,WAAW,EAAE;0BACf;wBAAE;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACFrL,OAAA;0BAAAiL,QAAA,EAAOpH,OAAO,CAAC+B;wBAAK;0BAAAsF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLrL,OAAA;sBAAI0L,SAAS,EAAC,aAAa;sBAAAT,QAAA,gBACzBjL,OAAA;wBAAK0L,SAAS,EAAC,MAAM;wBAAAT,QAAA,gBACnBjL,OAAA;0BAAAiL,QAAA,EAAQ;wBAAM;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACxH,OAAO,CAACC,UAAU,EAAC,QAC9C;sBAAA;wBAAAoH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNrL,OAAA;wBAAK0L,SAAS,EAAC,MAAM;wBAAAT,QAAA,gBACnBjL,OAAA;0BAAAiL,QAAA,EAAQ;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,EAAC,GAAG,eACnCrL,OAAA;0BAAM0L,SAAS,EAAE7H,OAAO,CAACK,cAAc,GAAG,EAAE,GAAG,qBAAqB,GAAG,EAAG;0BAAA+G,QAAA,EACvEpH,OAAO,CAACK,cAAc,KAAK,IAAI,GAAG,GAAGL,OAAO,CAACK,cAAc,QAAQ,GAAG;wBAAK;0BAAAgH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxE,CAAC,EACNxH,OAAO,CAACK,cAAc,KAAK,IAAI,IAAIL,OAAO,CAACC,UAAU,GAAG,CAAC,iBACxD9D,OAAA;0BAAK0L,SAAS,EAAC,MAAM;0BAAAT,QAAA,eACnBjL,OAAA,CAACnB,WAAW;4BACVgO,GAAG,EAAEC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAGlJ,OAAO,CAACK,cAAc,GAAGL,OAAO,CAACC,UAAU,GAAI,GAAG,CAAE;4BACxED,OAAO,EACLA,OAAO,CAACK,cAAc,GAAG,GAAG,GAAGL,OAAO,CAACC,UAAU,GAAG,QAAQ,GAC5DD,OAAO,CAACK,cAAc,GAAG,GAAG,GAAGL,OAAO,CAACC,UAAU,GAAG,SAAS,GAAG,SACjE;4BACDyH,KAAK,EAAE;8BAAEzB,MAAM,EAAE;4BAAM;0BAAE;4BAAAoB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACNrL,OAAA;wBAAAiL,QAAA,eACEjL,OAAA,CAACxB,MAAM;0BACLwO,IAAI,EAAC,IAAI;0BACTnJ,OAAO,EAAC,cAAc;0BACtBgI,OAAO,EAAEA,CAAA,KAAMvK,QAAQ,CAAC,qBAAqBuC,OAAO,CAACxD,EAAE,EAAE,CAAE;0BAAA4K,QAAA,EAC5D;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLrL,OAAA;sBAAI0L,SAAS,EAAC,aAAa;sBAAAT,QAAA,GAAC,MAAI,EAACpH,OAAO,CAACE,cAAc,EAAC,OAAK;oBAAA;sBAAAmH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClErL,OAAA;sBAAI0L,SAAS,EAAC,qBAAqB;sBAAAT,QAAA,GAAC,MAC9B,EAAC,CAACpH,OAAO,CAACK,cAAc,GAAGL,OAAO,CAACE,cAAc,EAAEC,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAkH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC,EACJjK,kBAAkB,iBACjBpB,OAAA;sBAAI0L,SAAS,EAAC,aAAa;sBAAAT,QAAA,eACzBjL,OAAA,CAACxB,MAAM;wBACLqF,OAAO,EAAC,QAAQ;wBAChBmJ,IAAI,EAAC,IAAI;wBACTnB,OAAO,EAAEA,CAAA,KAAMzG,iBAAiB,CAACvB,OAAO,CAAE;wBAC1CoJ,QAAQ,EAAE,CAAC3M,YAAY,CAACoD,QAAQ,IAAIpD,YAAY,CAACoD,QAAQ,CAACiB,MAAM,IAAI,CAAE;wBACtEuI,KAAK,EAAE,CAAC5M,YAAY,CAACoD,QAAQ,IAAIpD,YAAY,CAACoD,QAAQ,CAACiB,MAAM,IAAI,CAAC,GAAG,gCAAgC,GAAG,gBAAiB;wBAAAsG,QAAA,gBAEzHjL,OAAA,CAACR,OAAO;0BAACkM,SAAS,EAAC;wBAAM;0BAAAR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,WAC9B;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CACL;kBAAA,GAhEMxH,OAAO,CAACxD,EAAE;oBAAA6K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiEf,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACRrL,OAAA;kBAAO0L,SAAS,EAAC,UAAU;kBAAAT,QAAA,eACzBjL,OAAA;oBAAAiL,QAAA,gBACEjL,OAAA;sBAAImN,OAAO,EAAC,GAAG;sBAACzB,SAAS,EAAC,kBAAkB;sBAAAT,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxDrL,OAAA;sBAAI0L,SAAS,EAAC,aAAa;sBAAAT,QAAA,GAAE3K,YAAY,CAACoD,QAAQ,CAACiB,MAAM,EAAC,WAAS;oBAAA;sBAAAuG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxErL,OAAA;sBAAI0L,SAAS,EAAC,kCAAkC;sBAACyB,OAAO,EAAE/L,kBAAkB,GAAG,CAAC,GAAG,CAAE;sBAAA6J,QAAA,GAAC,MAChF,EAAChH,qBAAqB,CAAC,CAAC;oBAAA;sBAAAiH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGZrL,OAAA,CAAClB,KAAK;MAACsO,IAAI,EAAE7L,eAAgB;MAAC8L,MAAM,EAAEhH,gBAAiB;MAACiH,QAAQ;MAAArC,QAAA,gBAC9DjL,OAAA,CAAClB,KAAK,CAACkN,MAAM;QAACuB,WAAW;QAAAtC,QAAA,eACvBjL,OAAA,CAAClB,KAAK,CAAC0O,KAAK;UAAAvC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACfrL,OAAA,CAAClB,KAAK,CAACmN,IAAI;QAAAhB,QAAA,EACRxJ,eAAe,iBACdzB,OAAA,CAAAE,SAAA;UAAA+K,QAAA,gBACEjL,OAAA;YAAAiL,QAAA,EAAG;UAAoD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3DrL,OAAA,CAACzB,KAAK;YAACgO,QAAQ;YAACS,IAAI,EAAC,IAAI;YAACtB,SAAS,EAAC,MAAM;YAAAT,QAAA,eACxCjL,OAAA;cAAAiL,QAAA,gBACEjL,OAAA;gBAAAiL,QAAA,gBACEjL,OAAA;kBAAAiL,QAAA,eAAIjL,OAAA;oBAAAiL,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChCrL,OAAA;kBAAAiL,QAAA,eACEjL,OAAA;oBAAK0L,SAAS,EAAC,2BAA2B;oBAAAT,QAAA,gBACxCjL,OAAA;sBACEuL,KAAK,EAAE;wBACL1B,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE,MAAM;wBACd2C,eAAe,EAAEhL,eAAe,CAACmE,KAAK;wBACtC8G,YAAY,EAAE,KAAK;wBACnBC,MAAM,EAAE,mBAAmB;wBAC3BC,WAAW,EAAE;sBACf;oBAAE;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EACD5J,eAAe,CAACkE,UAAU,IAAIlE,eAAe,CAACmE,KAAK;kBAAA;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACLrL,OAAA;gBAAAiL,QAAA,gBACEjL,OAAA;kBAAAiL,QAAA,eAAIjL,OAAA;oBAAAiL,QAAA,EAAQ;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrCrL,OAAA;kBAAAiL,QAAA,GAAKxJ,eAAe,CAACqC,UAAU,EAAC,QAAM;gBAAA;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACLrL,OAAA;gBAAAiL,QAAA,gBACEjL,OAAA;kBAAAiL,QAAA,eAAIjL,OAAA;oBAAAiL,QAAA,EAAQ;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzCrL,OAAA;kBAAAiL,QAAA,GAAKxJ,eAAe,CAACyC,cAAc,EAAC,QAAM;gBAAA;kBAAAgH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACLrL,OAAA;gBAAAiL,QAAA,gBACEjL,OAAA;kBAAAiL,QAAA,eAAIjL,OAAA;oBAAAiL,QAAA,EAAQ;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzCrL,OAAA;kBAAAiL,QAAA,GAAI,MAAI,EAACxJ,eAAe,CAACsC,cAAc,EAAC,OAAK;gBAAA;kBAAAmH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEPxJ,eAAe,iBACd7B,OAAA,CAACtB,KAAK;YAACmF,OAAO,EAAC,SAAS;YAAC6H,SAAS,EAAC,MAAM;YAAAT,QAAA,gBACvCjL,OAAA;cAAAiL,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uFACzB,eAAArL,OAAA;cAAK0L,SAAS,EAAC,MAAM;cAAAT,QAAA,eACnBjL,OAAA;gBAAAiL,QAAA,EAAO;cAEP;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EAEA,CAACxJ,eAAe,IAAIvB,YAAY,IAAIA,YAAY,CAACoD,QAAQ,IAAIpD,YAAY,CAACoD,QAAQ,CAACiB,MAAM,GAAG,CAAC,iBAC5F3E,OAAA,CAACtB,KAAK;YAACmF,OAAO,EAAC,SAAS;YAAC6H,SAAS,EAAC,MAAM;YAAAT,QAAA,gBACvCjL,OAAA;cAAAiL,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,0EAC7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR,EAEA/K,YAAY,KAAK,CAACA,YAAY,CAACoD,QAAQ,IAAIpD,YAAY,CAACoD,QAAQ,CAACiB,MAAM,IAAI,CAAC,CAAC,iBAC5E3E,OAAA,CAACtB,KAAK;YAACmF,OAAO,EAAC,SAAS;YAAC6H,SAAS,EAAC,MAAM;YAAAT,QAAA,gBACvCjL,OAAA;cAAAiL,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,+JAC3B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA,eACD;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACbrL,OAAA,CAAClB,KAAK,CAAC2O,MAAM;QAAAxC,QAAA,gBACXjL,OAAA,CAACxB,MAAM;UAACqF,OAAO,EAAC,WAAW;UAACgI,OAAO,EAAExF,gBAAiB;UAAA4E,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrL,OAAA,CAACxB,MAAM;UACLqF,OAAO,EAAC,QAAQ;UAChBgI,OAAO,EAAEvG,aAAc;UACvB2H,QAAQ,EAAEtL,UAAU,IAAIE,eAAe,IAAKvB,YAAY,IAAIA,YAAY,CAACoD,QAAQ,IAAIpD,YAAY,CAACoD,QAAQ,CAACiB,MAAM,IAAI,CAAG;UAAAsG,QAAA,EAEvHtJ,UAAU,gBACT3B,OAAA,CAAAE,SAAA;YAAA+K,QAAA,gBACEjL,OAAA,CAACrB,OAAO;cAAC+O,EAAE,EAAC,MAAM;cAAC/B,SAAS,EAAC,QAAQ;cAACqB,IAAI,EAAC,IAAI;cAACpB,IAAI,EAAC,QAAQ;cAAC,eAAY,MAAM;cAACF,SAAS,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEtG;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRrL,OAAA,CAAClB,KAAK;MAACsO,IAAI,EAAErL,YAAa;MAACsL,MAAM,EAAEA,CAAA,KAAMrL,eAAe,CAAC,KAAK,CAAE;MAAAiJ,QAAA,gBAC9DjL,OAAA,CAAClB,KAAK,CAACkN,MAAM;QAACuB,WAAW;QAAAtC,QAAA,eACvBjL,OAAA,CAAClB,KAAK,CAAC0O,KAAK;UAAAvC,QAAA,gBACVjL,OAAA,CAACN,SAAS;YAACgM,SAAS,EAAC;UAAmB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iCAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfrL,OAAA,CAAClB,KAAK,CAACmN,IAAI;QAAAhB,QAAA,gBACTjL,OAAA;UAAG0L,SAAS,EAAC,iBAAiB;UAAAT,QAAA,GAAC,mEACoC,EAAC3K,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyH,WAAW,EAAC,yGAE9F;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJrL,OAAA,CAACtB,KAAK;UAACmF,OAAO,EAAC,MAAM;UAAAoH,QAAA,gBACnBjL,OAAA,CAACJ,OAAO;YAAC8L,SAAS,EAAC;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BrL,OAAA;YAAAiL,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,+HACxB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACbrL,OAAA,CAAClB,KAAK,CAAC2O,MAAM;QAAAxC,QAAA,gBACXjL,OAAA,CAACxB,MAAM;UAACqF,OAAO,EAAC,WAAW;UAACgI,OAAO,EAAEA,CAAA,KAAM7J,eAAe,CAAC,KAAK,CAAE;UAAAiJ,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrL,OAAA,CAACxB,MAAM;UACLqF,OAAO,EAAC,SAAS;UACjBgI,OAAO,EAAErF,WAAY;UACrByG,QAAQ,EAAEhL,UAAW;UAAAgJ,QAAA,EAEpBhJ,UAAU,gBACTjC,OAAA,CAAAE,SAAA;YAAA+K,QAAA,gBACEjL,OAAA,CAACrB,OAAO;cACN+O,EAAE,EAAC,MAAM;cACT/B,SAAS,EAAC,QAAQ;cAClBqB,IAAI,EAAC,IAAI;cACTpB,IAAI,EAAC,QAAQ;cACb,eAAY,MAAM;cAClBF,SAAS,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,qBAEJ;UAAA,eAAE,CAAC,gBAEHrL,OAAA,CAAAE,SAAA;YAAA+K,QAAA,gBACEjL,OAAA,CAACL,cAAc;cAAC+L,SAAS,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBACrC;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRrL,OAAA,CAAClB,KAAK;MAACsO,IAAI,EAAEjL,oBAAqB;MAACkL,MAAM,EAAEA,CAAA,KAAMjL,uBAAuB,CAAC,KAAK,CAAE;MAAA6I,QAAA,gBAC9EjL,OAAA,CAAClB,KAAK,CAACkN,MAAM;QAACuB,WAAW;QAAAtC,QAAA,eACvBjL,OAAA,CAAClB,KAAK,CAAC0O,KAAK;UAAAvC,QAAA,gBACVjL,OAAA,CAACN,SAAS;YAACgM,SAAS,EAAC;UAAmB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,0BAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfrL,OAAA,CAAClB,KAAK,CAACmN,IAAI;QAAAhB,QAAA,gBACTjL,OAAA;UAAG0L,SAAS,EAAC,iBAAiB;UAAAT,QAAA,GAAC,kFACmD,EAAC3K,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyH,WAAW,EAAC,iHAE7G;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJrL,OAAA,CAACtB,KAAK;UAACmF,OAAO,EAAC,MAAM;UAAAoH,QAAA,gBACnBjL,OAAA,CAACJ,OAAO;YAAC8L,SAAS,EAAC;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BrL,OAAA;YAAAiL,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,kGACxB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACbrL,OAAA,CAAClB,KAAK,CAAC2O,MAAM;QAAAxC,QAAA,gBACXjL,OAAA,CAACxB,MAAM;UAACqF,OAAO,EAAC,WAAW;UAACgI,OAAO,EAAEA,CAAA,KAAMzJ,uBAAuB,CAAC,KAAK,CAAE;UAAA6I,QAAA,EAAC;QAE3E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrL,OAAA,CAACxB,MAAM;UACLqF,OAAO,EAAC,SAAS;UACjBgI,OAAO,EAAEjB,mBAAoB;UAC7BqC,QAAQ,EAAE5K,kBAAmB;UAAA4I,QAAA,EAE5B5I,kBAAkB,gBACjBrC,OAAA,CAAAE,SAAA;YAAA+K,QAAA,gBACEjL,OAAA,CAACrB,OAAO;cACN+O,EAAE,EAAC,MAAM;cACT/B,SAAS,EAAC,QAAQ;cAClBqB,IAAI,EAAC,IAAI;cACTpB,IAAI,EAAC,QAAQ;cACb,eAAY,MAAM;cAClBF,SAAS,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,qBAEJ;UAAA,eAAE,CAAC,gBAEHrL,OAAA,CAAAE,SAAA;YAAA+K,QAAA,gBACEjL,OAAA,CAACL,cAAc;cAAC+L,SAAS,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBACrC;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACjL,EAAA,CA1kCID,kBAAkB;EAAA,QACPrC,SAAS,EAQPC,WAAW;AAAA;AAAA4P,EAAA,GATxBxN,kBAAkB;AA4kCxB,eAAeA,kBAAkB;AAAC,IAAAwN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}